using AutoMapper;
using Azure.Messaging.ServiceBus;
using BlueTape.Common.ExceptionHandling.Exceptions.Base;
using BlueTape.Common.ExceptionHandling.Extensions;
using BlueTape.Common.Extensions.Abstractions;
using BlueTape.PaymentService.API.Constants;
using BlueTape.PaymentService.API.Extensions;
using BlueTape.PaymentService.Application.Abstractions.Services.Notification;
using BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation;
using BlueTape.PaymentService.Domain.Constants;
using BlueTape.PaymentService.Domain.Exceptions;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.BaseDisbursement;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.DrawDisbursement;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.DrawRepayment;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.DrawRepaymentCard;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.DrawRepaymentManual;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.FactoringDisbursement;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.FactoringFinalPayment;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.FinalPayment;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.IhcRepayment;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.InvoiceDisbursementV2;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.InvoicePayment;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.InvoicePaymentV2;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.ManualPaymentPull;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.SubscriptionFeePayment;
using BlueTape.PaymentService.Domain.Models.PaymentRequest;
using FluentValidation;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Serilog.Context;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace BlueTape.Functions.PaymentJob;

public class PaymentRequestConsumer(ILogger<PaymentRequestConsumer> logger,
    IMapper mapper,
    IInvoicePaymentRequestCreationService invoiceBasePaymentRequestService,
    IInvoicePaymentV2RequestCreationService invoiceBasePaymentV2RequestService,
    IInvoiceDisbursementV2RequestCreationService invoiceDisbursementV2RequestService,
    IDrawRepaymentRequestCreationService drawRepaymentRequestService,
    IDrawRepaymentCardRequestCreationService drawRepaymentCardRequestService,
    IDrawRepaymentManualRequestCreationService drawRepaymentManualRequestService,
    IDrawDisbursementRequestCreatorService drawDisbursementRequestService,
    IFinalPaymentRequestCreationService finalBasePaymentRequestService,
    IFactoringDisbursementRequestCreationService factoringDisbursementRequestCreationService,
    IFactoringFinalPaymentRequestCreationService factoringFinalBasePaymentRequestCreationService,
    IManualPaymentPullCreationService manualPaymentPullCreationService,
    ISubscriptionFeePaymentRequestCreationService subscriptionFeePaymentRequestCreationService,
    IUnifiedPaymentCreationService unifiedPaymentCreationService,
    IValidator<InvoicePaymentRequestMessage> payNowValidator,
    IValidator<DrawRepaymentRequestMessage> tradeCreditValidator,
    IValidator<BaseDisbursementRequestMessage> baseDisbursementValidator,
    IValidator<SubscriptionFeePaymentRequestMessage> subscriptionFeeValidator,
    ISlackNotificationService notificationService,
    ITraceIdAccessor traceIdAccessor)
{
    private readonly JsonSerializerOptions _serializerOptions = new(JsonSerializerDefaults.Web)
    {
        Converters = { new JsonStringEnumConverter() },
        PropertyNameCaseInsensitive = true,
        WriteIndented = false
    };

    [Function(nameof(PaymentRequestConsumer))]
    public async Task Run([ServiceBusTrigger($"%{InfrastructureConstants.PaymentRequestQueueName}%", Connection = $"{InfrastructureConstants.PaymentRequestQueueConnection}")]
        ServiceBusReceivedMessage message,
        ServiceBusMessageActions messageActions,
        CancellationToken ct)
    {
        traceIdAccessor.TraceId = $"{Guid.NewGuid()}-{nameof(PaymentRequestConsumer)}";

        using (GlobalLogContext.PushProperty("functionName", nameof(PaymentRequestConsumer)))
        using (GlobalLogContext.PushProperty("BlueTapeCorrelationId", traceIdAccessor.TraceId))
        {
            logger.LogInformation("Got message from the queue: Session: {sessionId}, \n Message: {messageId}, \n Body: {@message}", message.SessionId, message.MessageId, Encoding.UTF8.GetString(message.Body));

            try
            {
                var parsedMessage = message.Body.ToObjectFromJson<BasePaymentRequestMessage?>();
                logger.LogInformation($"Received payment request message, templateCode: {parsedMessage.FlowTemplateCode}, message body: {Encoding.UTF8.GetString(message.Body)}");

                switch (parsedMessage?.FlowTemplateCode)
                {
                    case DomainConstants.InvoicePaymentCard:
                    case DomainConstants.InvoicePayment:
                        await HandlePayNowMessage(message, ct);
                        break;
                    case DomainConstants.InvoicePaymentV2:
                        await HandleInvoicePaymentV2Message(message, ct);
                        break;
                    case DomainConstants.InvoiceDisbursementV2:
                        await HandleInvoiceDisbursementV2Message(message, ct);
                        break;
                    case DomainConstants.DrawRepaymentCard:
                        await HandleDrawRepaymentCardMessage(message, ct);
                        break;
                    case DomainConstants.DrawRepayment:
                        await HandleDrawRepaymentMessage(message, ct);
                        break;
                    case DomainConstants.DrawRepaymentManual:
                        await HandleDrawRepaymentManualMessage(message, ct);
                        break;
                    case DomainConstants.FinalPayment:
                    case DomainConstants.FinalPaymentV2:
                        await HandleFinalPaymentMessage(message, ct);
                        break;
                    case DomainConstants.FactoringFinalPayment:
                        await HandleFactoringFinalPaymentMessage(message, ct);
                        break;
                    case DomainConstants.FactoringDisbursement:
                        await HandleFactoringDisbursementMessage(message, ct);
                        break;
                    case DomainConstants.DrawDisbursement:
                        await HandleDrawDisbursementMessage(message, ct);
                        break;
                    case DomainConstants.ManualPaymentPull:
                        await HandleManualPaymentPullMessage(message, ct);
                        break;
                    case DomainConstants.SubscriptionFeePayment:
                        await HandleSubscriptionFeePaymentMessage(message, ct);
                        break;
                    case DomainConstants.IhcRepayment:
                        await HandleIhcRepaymentMessage(message, ct);
                        break;
                    default:
                        throw new ArgumentNullException($"Invalid template code: {parsedMessage?.FlowTemplateCode}");
                }

                logger.LogInformation($"Payment Request Message was processed successfully");
            }
            catch (DomainException ex)
            {
                await notificationService.Notify(ex.GetSlackEventMessageBody(EnvironmentExtensions.GetExecutionEnvironment(), ConfigurationKeys.ProjectValue), ct);
                logger.LogError("A business logic exception occurred while processing the message: {messageId}, \n  Exception: {ex}", message.MessageId, ex);
                await messageActions.DeadLetterMessageAsync(message, cancellationToken: ct);
                throw;
            }
            catch (FluentValidationException ex)
            {
                await notificationService.Notify(ex.GetSlackEventMessageBody(EnvironmentExtensions.GetExecutionEnvironment(), ConfigurationKeys.ProjectValue), ct);
                logger.LogError("A business logic exception occurred while processing the message: {messageId}, \n  Exception: {ex}", message.MessageId, ex);
                await messageActions.DeadLetterMessageAsync(message, cancellationToken: ct);
                throw;
            }
            catch (Exception ex)
            {
                await notificationService.Notify(ex.GetSlackEventMessageBody(EnvironmentExtensions.GetExecutionEnvironment(), ConfigurationKeys.ProjectValue), ct);
                logger.LogError("A business logic exception occurred while processing the message: {messageId}, \n  Exception: {ex}", message.MessageId, ex);
                await messageActions.DeadLetterMessageAsync(message, cancellationToken: ct);
                throw;
            }
        }
    }

    public async Task HandleSubscriptionFeePaymentMessage(ServiceBusReceivedMessage message, CancellationToken ct)
    {
        var parsedMessage = message.Body.ToObjectFromJson<SubscriptionFeePaymentRequestMessage?>(_serializerOptions);

        logger.LogInformation("Validation started for Subscription Fee Payment Request Message");
        CheckIfMessageIsNull(parsedMessage, message.SessionId, message.MessageId);
        await subscriptionFeeValidator.ValidateAndThrowsManualAsync(parsedMessage!, ct);

        var createPaymentRequest = mapper.Map<CreatePaymentRequestModel>(parsedMessage);
        await unifiedPaymentCreationService.CreatePaymentRequest(createPaymentRequest!, parsedMessage!.CreatedBy, ct);
    }

    public async Task HandleIhcRepaymentMessage(ServiceBusReceivedMessage message, CancellationToken ct)
    {
        var parsedMessage = message.Body.ToObjectFromJson<IhcRepaymentRequestMessage?>(_serializerOptions);

        logger.LogInformation("Validation started for IHC Repayment Request Message");
        CheckIfMessageIsNull(parsedMessage, message.SessionId, message.MessageId);

        var createPaymentRequest = mapper.Map<CreatePaymentRequestModel>(parsedMessage);
        await unifiedPaymentCreationService.CreatePaymentRequest(createPaymentRequest!, parsedMessage!.CreatedBy, ct);
    }

    private async Task HandlePayNowMessage(ServiceBusReceivedMessage message, CancellationToken ct)
    {
        var parsedMessage = message.Body.ToObjectFromJson<InvoicePaymentRequestMessage?>(_serializerOptions);

        logger.LogInformation("Validation started for Pay Now Request Message");
        CheckIfMessageIsNull(parsedMessage, message.SessionId, message.MessageId);
        await payNowValidator.ValidateAndThrowsManualAsync(parsedMessage!, ct);

        var createPaymentRequest = mapper.Map<CreatePaymentRequestModel>(parsedMessage);
        await unifiedPaymentCreationService.CreatePaymentRequest(createPaymentRequest!, parsedMessage!.CreatedBy, ct);
    }

    private async Task HandleInvoicePaymentV2Message(ServiceBusReceivedMessage message, CancellationToken ct)
    {
        var parsedMessage = message.Body.ToObjectFromJson<InvoicePaymentV2RequestMessage?>(_serializerOptions);

        logger.LogInformation("Validation started for Pay Now Request Message");
        CheckIfMessageIsNull(parsedMessage, message.SessionId, message.MessageId);
        await payNowValidator.ValidateAndThrowsManualAsync(parsedMessage!, ct);

        var createPaymentRequest = mapper.Map<CreatePaymentRequestModel>(parsedMessage);
        await unifiedPaymentCreationService.CreatePaymentRequest(createPaymentRequest!, parsedMessage!.CreatedBy, ct);
    }

    private async Task HandleInvoiceDisbursementV2Message(ServiceBusReceivedMessage message, CancellationToken ct)
    {
        var parsedMessage = message.Body.ToObjectFromJson<InvoiceDisbursementV2RequestMessage?>(_serializerOptions);

        logger.LogInformation("Validation started for Pay Now Request Message");
        CheckIfMessageIsNull(parsedMessage, message.SessionId, message.MessageId);
        await payNowValidator.ValidateAndThrowsManualAsync(parsedMessage!, ct);

        var createPaymentRequest = mapper.Map<CreatePaymentRequestModel>(parsedMessage);
        await unifiedPaymentCreationService.CreatePaymentRequest(createPaymentRequest!, parsedMessage!.CreatedBy, ct);
    }

    private async Task HandleDrawDisbursementMessage(ServiceBusReceivedMessage message, CancellationToken ct)
    {
        var parsedMessage = message.Body.ToObjectFromJson<DrawDisbursementRequestMessage?>(_serializerOptions);

        logger.LogInformation("Validation started for Trade Credit Request Message");
        CheckIfMessageIsNull(parsedMessage, message.SessionId, message.MessageId);
        await baseDisbursementValidator.ValidateAndThrowsManualAsync(parsedMessage!, ct);

        var createPaymentRequest = mapper.Map<CreatePaymentRequestModel>(parsedMessage);
        await unifiedPaymentCreationService.CreatePaymentRequest(createPaymentRequest!, parsedMessage!.CreatedBy, ct);
    }

    private async Task HandleDrawRepaymentMessage(ServiceBusReceivedMessage message, CancellationToken ct)
    {
        var parsedMessage = message.Body.ToObjectFromJson<DrawRepaymentRequestMessage?>(_serializerOptions);

        logger.LogInformation("Validation started for Trade Credit Request Message");
        CheckIfMessageIsNull(parsedMessage, message.SessionId, message.MessageId);
        await tradeCreditValidator.ValidateAndThrowsManualAsync(parsedMessage!, ct);

        var createPaymentRequest = mapper.Map<CreatePaymentRequestModel>(parsedMessage);
        await unifiedPaymentCreationService.CreatePaymentRequest(createPaymentRequest!, parsedMessage!.CreatedBy, ct);
    }

    private async Task HandleDrawRepaymentCardMessage(ServiceBusReceivedMessage message, CancellationToken ct)
    {
        var parsedMessage = message.Body.ToObjectFromJson<DrawRepaymentCardRequestMessage?>(_serializerOptions);

        logger.LogInformation("Validation started for Trade Credit Request Message");
        CheckIfMessageIsNull(parsedMessage, message.SessionId, message.MessageId);
        await tradeCreditValidator.ValidateAndThrowsManualAsync(parsedMessage!, ct);

        var createPaymentRequest = mapper.Map<CreatePaymentRequestModel>(parsedMessage);
        await unifiedPaymentCreationService.CreatePaymentRequest(createPaymentRequest!, parsedMessage!.CreatedBy, ct);
    }

    private async Task HandleDrawRepaymentManualMessage(ServiceBusReceivedMessage message, CancellationToken ct)
    {
        var parsedMessage = message.Body.ToObjectFromJson<DrawRepaymentManualRequestMessage?>(_serializerOptions);

        logger.LogInformation("Validation started for Trade Credit Request Message");
        CheckIfMessageIsNull(parsedMessage, message.SessionId, message.MessageId);
        //await tradeCreditValidator.ValidateAndThrowsManualAsync(parsedMessage!, ct);

        var createPaymentRequest = mapper.Map<CreatePaymentRequestModel>(parsedMessage);
        await unifiedPaymentCreationService.CreatePaymentRequest(createPaymentRequest!, parsedMessage!.CreatedBy, ct);
    }

    private async Task HandleFinalPaymentMessage(ServiceBusReceivedMessage message, CancellationToken ct)
    {
        var parsedMessage = message.Body.ToObjectFromJson<FinalPaymentRequestMessage?>(_serializerOptions);

        logger.LogInformation("Validation started for Final Payment Request Message");
        CheckIfMessageIsNull(parsedMessage, message.SessionId, message.MessageId);
        await baseDisbursementValidator.ValidateAndThrowsManualAsync(parsedMessage!, ct);

        var createPaymentRequest = mapper.Map<CreatePaymentRequestModel>(parsedMessage);
        await unifiedPaymentCreationService.CreatePaymentRequest(createPaymentRequest!, parsedMessage!.CreatedBy, ct);
    }

    private async Task HandleFactoringDisbursementMessage(ServiceBusReceivedMessage message, CancellationToken ct)
    {
        var parsedMessage = message.Body.ToObjectFromJson<FactoringDisbursementRequestMessage?>(_serializerOptions);

        logger.LogInformation("Validation started for Factoring Disbursement Request Message");
        CheckIfMessageIsNull(parsedMessage, message.SessionId, message.MessageId);
        await baseDisbursementValidator.ValidateAndThrowsManualAsync(parsedMessage!, ct);

        var createPaymentRequest = mapper.Map<CreatePaymentRequestModel>(parsedMessage);
        await unifiedPaymentCreationService.CreatePaymentRequest(createPaymentRequest!, parsedMessage!.CreatedBy, ct);
    }

    private async Task HandleFactoringFinalPaymentMessage(ServiceBusReceivedMessage message, CancellationToken ct)
    {
        var parsedMessage = message.Body.ToObjectFromJson<FactoringFinalPaymentRequestMessage?>(_serializerOptions);

        logger.LogInformation("Validation started for Factoring Disbursement Request Message");
        CheckIfMessageIsNull(parsedMessage, message.SessionId, message.MessageId);
        await baseDisbursementValidator.ValidateAndThrowsManualAsync(parsedMessage!, ct);

        var createPaymentRequest = mapper.Map<CreatePaymentRequestModel>(parsedMessage);
        await unifiedPaymentCreationService.CreatePaymentRequest(createPaymentRequest!, parsedMessage!.CreatedBy, ct);
    }

    private async Task HandleManualPaymentPullMessage(ServiceBusReceivedMessage message, CancellationToken ct)
    {
        var parsedMessage = message.Body.ToObjectFromJson<ManualPaymentPullRequestMessage?>(_serializerOptions);

        logger.LogInformation("Validation started for Manual Payment Pull Request Message");
        CheckIfMessageIsNull(parsedMessage, message.SessionId, message.MessageId);

        var createPaymentRequest = mapper.Map<CreatePaymentRequestModel>(parsedMessage);
        await unifiedPaymentCreationService.CreatePaymentRequest(createPaymentRequest!, parsedMessage!.CreatedBy, ct);
    }

    private void CheckIfMessageIsNull(object? message, string sessionId, string messageId)
    {
        if (message is not null) return;
        logger.LogError(
            "Unable to process message with empty body or invalid fields. Session: {sessionId}, \n Message: {messageId}",
            sessionId, messageId);
        throw new ArgumentNullException(nameof(message));
    }
}
