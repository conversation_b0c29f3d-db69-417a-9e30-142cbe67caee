﻿// <auto-generated />
using System;
using BlueTape.PaymentService.DataAccess.Contexts;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace BlueTape.PaymentService.DataAccess.Migrations
{
    [DbContext(typeof(DatabaseContext))]
    [Migration("20250602081734_AddProcessingFees")]
    partial class AddProcessingFees
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.HasSequence("PublicTransactionIdentifiers")
                .StartsAt(0L)
                .HasMin(0L);

            modelBuilder.Entity("BlueTape.PaymentService.Domain.Entities.EventLogEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Payload")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("PaymentRequestId")
                        .HasColumnType("uuid");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<Guid?>("TransactionHistoryId")
                        .HasColumnType("uuid");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("PaymentRequestId");

                    b.HasIndex("TransactionHistoryId");

                    b.ToTable("EventLogs");
                });

            modelBuilder.Entity("BlueTape.PaymentService.Domain.Entities.ForbiddenCompanyEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CompanyId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("PauseReason")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.ToTable("ForbiddenCompanies");
                });

            modelBuilder.Entity("BlueTape.PaymentService.Domain.Entities.PaymentRequestCommandEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("PaymentRequestId")
                        .HasColumnType("uuid");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("StepName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("TransactionId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("PaymentRequestId");

                    b.HasIndex("TransactionId");

                    b.ToTable("PaymentRequestCommands");
                });

            modelBuilder.Entity("BlueTape.PaymentService.Domain.Entities.PaymentRequestDetailsEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("DrawId")
                        .HasColumnType("uuid");

                    b.Property<string>("ExternalReferenceNumber")
                        .HasColumnType("text");

                    b.Property<int?>("FundingSource")
                        .HasColumnType("integer");

                    b.Property<string>("InvoiceNumber")
                        .HasColumnType("text");

                    b.Property<bool?>("IsPaused")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("LMSPaymentId")
                        .HasColumnType("uuid");

                    b.Property<string>("Metadata")
                        .HasColumnType("text");

                    b.Property<string>("ParentReferenceNumber")
                        .HasColumnType("text");

                    b.Property<string>("PauseComments")
                        .HasColumnType("text");

                    b.Property<string>("PauseCreatedBy")
                        .HasColumnType("text");

                    b.Property<int?>("PauseReason")
                        .HasColumnType("integer");

                    b.Property<Guid>("PaymentRequestId")
                        .HasColumnType("uuid");

                    b.Property<string>("Reason")
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("PaymentRequestId")
                        .IsUnique();

                    b.ToTable("PaymentRequestDetails");
                });

            modelBuilder.Entity("BlueTape.PaymentService.Domain.Entities.PaymentRequestEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal>("Amount")
                        .HasColumnType("numeric");

                    b.Property<int>("ConfirmationType")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("ConfirmedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ConfirmedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid?>("CreditId")
                        .HasColumnType("uuid");

                    b.Property<string>("Currency")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateOnly>("Date")
                        .HasColumnType("date");

                    b.Property<DateTime?>("ExecuteAfter")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal>("FeeAmount")
                        .HasColumnType("numeric");

                    b.Property<string>("FlowTemplateCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("ManualPaymentMethod")
                        .HasColumnType("integer");

                    b.Property<int>("MerchantAchDelayInBusinessDays")
                        .HasColumnType("integer");

                    b.Property<string>("PayeeId")
                        .HasColumnType("text");

                    b.Property<string>("PayerId")
                        .HasColumnType("text");

                    b.Property<int>("PaymentMethod")
                        .HasColumnType("integer");

                    b.Property<int>("PaymentSubscription")
                        .HasColumnType("integer");

                    b.Property<int>("RequestType")
                        .HasColumnType("integer");

                    b.Property<string>("SellerId")
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<int>("SubjectType")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("PaymentRequests");
                });

            modelBuilder.Entity("BlueTape.PaymentService.Domain.Entities.PaymentRequestFeeEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal>("Amount")
                        .HasColumnType("numeric");

                    b.Property<string>("CompanyId")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("PaymentRequestId")
                        .HasColumnType("uuid");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("PaymentRequestId");

                    b.ToTable("PaymentRequestFees");
                });

            modelBuilder.Entity("BlueTape.PaymentService.Domain.Entities.PaymentRequestPayableEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal>("Amount")
                        .HasColumnType("numeric");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal>("Discount")
                        .HasColumnType("numeric");

                    b.Property<string>("PayableId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("PaymentRequestId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("PaymentRequestId");

                    b.ToTable("PaymentRequestPayables");
                });

            modelBuilder.Entity("BlueTape.PaymentService.Domain.Entities.PaymentTransactionEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal>("Amount")
                        .HasColumnType("numeric");

                    b.Property<DateTime?>("ClearedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Currency")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateOnly>("Date")
                        .HasColumnType("date");

                    b.Property<decimal>("Discount")
                        .HasColumnType("numeric");

                    b.Property<DateTime>("ExecutedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastResult")
                        .HasColumnType("text");

                    b.Property<string>("LastResultCode")
                        .HasColumnType("text");

                    b.Property<string>("MetaData")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("OriginatorAccountId")
                        .HasColumnType("text");

                    b.Property<int>("PaymentMethod")
                        .HasColumnType("integer");

                    b.Property<Guid>("PaymentRequestId")
                        .HasColumnType("uuid");

                    b.Property<decimal?>("ProcessingFees")
                        .HasColumnType("numeric");

                    b.Property<int>("Provider")
                        .HasColumnType("integer");

                    b.Property<string>("PublicTransactionNumber")
                        .HasColumnType("text");

                    b.Property<string>("ReceiverAccountId")
                        .HasColumnType("text");

                    b.Property<string>("ReferenceNumber")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("SequenceNumber")
                        .HasColumnType("integer");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("TransactionNumber")
                        .HasColumnType("text");

                    b.Property<int>("TransactionType")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("PaymentRequestId");

                    b.HasIndex("TransactionNumber")
                        .IsUnique();

                    b.ToTable("PaymentTransactions");
                });

            modelBuilder.Entity("BlueTape.PaymentService.Domain.Entities.PaymentTransactionHistoryEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("ExecutedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("NewStatus")
                        .HasColumnType("integer");

                    b.Property<int>("OldStatus")
                        .HasColumnType("integer");

                    b.Property<int?>("PaymentRequestStatus")
                        .HasColumnType("integer");

                    b.Property<string>("ResultCode")
                        .HasColumnType("text");

                    b.Property<string>("ResultText")
                        .HasColumnType("text");

                    b.Property<Guid>("TransactionId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("TransactionId");

                    b.ToTable("PaymentTransactionsHistory");
                });

            modelBuilder.Entity("BlueTape.PaymentService.Domain.Entities.EventLogEntity", b =>
                {
                    b.HasOne("BlueTape.PaymentService.Domain.Entities.PaymentRequestEntity", "PaymentRequest")
                        .WithMany("PaymentRequestNotifications")
                        .HasForeignKey("PaymentRequestId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BlueTape.PaymentService.Domain.Entities.PaymentTransactionHistoryEntity", "TransactionHistory")
                        .WithMany("RequestNotificationEntities")
                        .HasForeignKey("TransactionHistoryId");

                    b.Navigation("PaymentRequest");

                    b.Navigation("TransactionHistory");
                });

            modelBuilder.Entity("BlueTape.PaymentService.Domain.Entities.PaymentRequestCommandEntity", b =>
                {
                    b.HasOne("BlueTape.PaymentService.Domain.Entities.PaymentRequestEntity", "PaymentRequest")
                        .WithMany("PaymentRequestCommands")
                        .HasForeignKey("PaymentRequestId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BlueTape.PaymentService.Domain.Entities.PaymentTransactionEntity", "Transaction")
                        .WithMany()
                        .HasForeignKey("TransactionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("PaymentRequest");

                    b.Navigation("Transaction");
                });

            modelBuilder.Entity("BlueTape.PaymentService.Domain.Entities.PaymentRequestDetailsEntity", b =>
                {
                    b.HasOne("BlueTape.PaymentService.Domain.Entities.PaymentRequestEntity", "PaymentRequest")
                        .WithOne("PaymentRequestDetails")
                        .HasForeignKey("BlueTape.PaymentService.Domain.Entities.PaymentRequestDetailsEntity", "PaymentRequestId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("PaymentRequest");
                });

            modelBuilder.Entity("BlueTape.PaymentService.Domain.Entities.PaymentRequestFeeEntity", b =>
                {
                    b.HasOne("BlueTape.PaymentService.Domain.Entities.PaymentRequestEntity", "PaymentRequest")
                        .WithMany("PaymentRequestFees")
                        .HasForeignKey("PaymentRequestId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("PaymentRequest");
                });

            modelBuilder.Entity("BlueTape.PaymentService.Domain.Entities.PaymentRequestPayableEntity", b =>
                {
                    b.HasOne("BlueTape.PaymentService.Domain.Entities.PaymentRequestEntity", "PaymentRequest")
                        .WithMany("PaymentRequestPayables")
                        .HasForeignKey("PaymentRequestId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("PaymentRequest");
                });

            modelBuilder.Entity("BlueTape.PaymentService.Domain.Entities.PaymentTransactionEntity", b =>
                {
                    b.HasOne("BlueTape.PaymentService.Domain.Entities.PaymentRequestEntity", "PaymentRequest")
                        .WithMany("Transactions")
                        .HasForeignKey("PaymentRequestId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("PaymentRequest");
                });

            modelBuilder.Entity("BlueTape.PaymentService.Domain.Entities.PaymentTransactionHistoryEntity", b =>
                {
                    b.HasOne("BlueTape.PaymentService.Domain.Entities.PaymentTransactionEntity", "Transaction")
                        .WithMany("TransactionHistories")
                        .HasForeignKey("TransactionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Transaction");
                });

            modelBuilder.Entity("BlueTape.PaymentService.Domain.Entities.PaymentRequestEntity", b =>
                {
                    b.Navigation("PaymentRequestCommands");

                    b.Navigation("PaymentRequestDetails");

                    b.Navigation("PaymentRequestFees");

                    b.Navigation("PaymentRequestNotifications");

                    b.Navigation("PaymentRequestPayables");

                    b.Navigation("Transactions");
                });

            modelBuilder.Entity("BlueTape.PaymentService.Domain.Entities.PaymentTransactionEntity", b =>
                {
                    b.Navigation("TransactionHistories");
                });

            modelBuilder.Entity("BlueTape.PaymentService.Domain.Entities.PaymentTransactionHistoryEntity", b =>
                {
                    b.Navigation("RequestNotificationEntities");
                });
#pragma warning restore 612, 618
        }
    }
}
