﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace BlueTape.PaymentService.DataAccess.Migrations
{
    /// <inheritdoc />
    public partial class AddProcessingFees : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "ProcessingFees",
                table: "PaymentTransactions",
                type: "numeric",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ProcessingFees",
                table: "PaymentTransactions");
        }
    }
}
