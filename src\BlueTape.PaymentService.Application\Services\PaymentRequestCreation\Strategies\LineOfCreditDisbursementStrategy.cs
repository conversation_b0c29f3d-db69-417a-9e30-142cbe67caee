using BlueTape.InvoiceService.Models.Invoice;
using BlueTape.LS.DTOs.Loan;
using BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation;
using BlueTape.PaymentService.Application.Abstractions.Validators;
using BlueTape.PaymentService.Application.Models;
using BlueTape.PaymentService.Domain.Constants;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Models.PaymentRequest;
using BlueTape.PaymentService.PaymentFlowTemplatesEngine.Abstractions;

namespace BlueTape.PaymentService.Application.Services.PaymentRequestCreation.Strategies;

/// <summary>
/// Strategy for handling LOC Disbursements requests (Advance, and Finals)
/// </summary>
public class LineOfCreditDisbursementStrategy : IPaymentCreationStrategy
{
    private readonly IPaymentRequestValidator _paymentRequestValidator;
    private readonly IPaymentFlowTemplatesEngine _templatesEngine;

    public LineOfCreditDisbursementStrategy(
        IPaymentRequestValidator paymentRequestValidator,
        IPaymentFlowTemplatesEngine templatesEngine)
    {
        _paymentRequestValidator = paymentRequestValidator;
        _templatesEngine = templatesEngine;
    }

    public bool CanHandle(string flowTemplateCode)
    {
        return flowTemplateCode is
            DomainConstants.DrawDisbursement or
            DomainConstants.FinalPayment or
            DomainConstants.FinalPaymentV2;
    }

    public async Task ValidatePaymentRequest(CreatePaymentRequestModel createPaymentRequest,
        List<InvoiceModel> existingInvoices, List<PaymentRequestPayableModel> processedPayables,
        LoanDto? loan, CancellationToken ct)
    {
        await _paymentRequestValidator.ValidateSupplierDisbursementRequest(
            createPaymentRequest, existingInvoices, processedPayables, ct);
    }

    public async Task SendNotification(CreatePaymentRequestModel createPaymentRequest,
        PaymentRequestEntity paymentRequestEntity, IEnumerable<InvoiceModel> existingInvoices,
        CancellationToken ct)
    {
        // Draw disbursement requests don't send notifications (ToDo: ledger, notifications)
        await Task.CompletedTask;
    }

    public async Task ProcessAdditionalLogic(CreatePaymentRequestModel createPaymentRequest, CancellationToken ct)
    {
        // Set delay for Line of credit advance disbursement
        if (createPaymentRequest.FlowTemplateCode == DomainConstants.DrawDisbursement)
        {
            createPaymentRequest.MerchantAchDelayInBusinessDays =
                _templatesEngine.GetPaymentRequestDelay(createPaymentRequest.FlowTemplateCode);
        }

        // Process merchant fees from payables discount for final payments
        if (createPaymentRequest.FlowTemplateCode is DomainConstants.FinalPayment or DomainConstants.FinalPaymentV2)
        {
            var merchantFees = GetMerchantFeesFromPayablesDiscount(
                createPaymentRequest.PaymentRequestPayables,
                createPaymentRequest.PayeeId!,
                FeeType.Merchant);

            if (merchantFees is not null && merchantFees.Any())
            {
                createPaymentRequest.PaymentRequestFees.AddRange(merchantFees);
            }
        }

        await Task.CompletedTask;
    }

    private static List<CreatePaymentRequestFeeModel>? GetMerchantFeesFromPayablesDiscount(
        IEnumerable<CreatePaymentRequestPayableModel> payables, string companyId, FeeType feeType)
    {
        if (!payables.Any())
            return null;

        var createDiscountDetails = payables
            .Where(x => x.Discount != 0)
            .Select(x => new CreatePaymentRequestFeeModel()
            {
                Amount = x.Discount,
                CompanyId = companyId,
                Type = feeType
            })
            .ToList();

        return createDiscountDetails;
    }
}
