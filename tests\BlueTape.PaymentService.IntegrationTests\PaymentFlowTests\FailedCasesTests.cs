﻿using BlueTape.PaymentService.Domain.Constants;
using BlueTape.PaymentService.IntegrationTests.PaymentFlowTests.Base;
using BlueTape.PaymentService.IntegrationTests.TestConstants;
using BlueTape.PaymentService.IntegrationTests.TestData;

namespace BlueTape.PaymentService.IntegrationTests.PaymentFlowTests;

[Collection(Configuration.SequentialExecution)]
public class FailedCasesTests() : BasePaymentFlowTest(dbName: "failedCasesDb")
{
    private readonly int firstStepNumber = 1;

    [Theory]
    [InlineData(DomainConstants.FactoringFinalPayment)]
    [InlineData(DomainConstants.InvoicePayment)]
    [InlineData(DomainConstants.InvoicePaymentV2)]
    [InlineData(DomainConstants.DrawRepayment)]
    [InlineData(DomainConstants.FinalPayment)]
    [InlineData(DomainConstants.FinalPaymentV2)]
    [InlineData(DomainConstants.InvoiceDisbursementV2)]
    [InlineData(DomainConstants.FactoringDisbursement)]
    [InlineData(DomainConstants.DrawDisbursement)]
    [InlineData(DomainConstants.InvoicePaymentCard)]
    [InlineData(DomainConstants.DrawRepaymentCard)]
    [InlineData(DomainConstants.DrawRepaymentManual)]
    [InlineData(DomainConstants.SubscriptionFeePayment)]
    public async Task Payment_AionServiceException_TransactionStatusError(string templateCode)
    {
        var message = PaymentRequestData.CreateValidPaymentRequest(templateCode, amount: Configuration.HttpClientExceptionAmount);
        var paymentRequest = await CreateAndAssertPayment(message);
        var paymentRequestId = paymentRequest.Id;
        var details = await GetPaymentRequestById(paymentRequestId);

        var commandToExecute = details.PaymentRequestCommands.First(x => x.Status == CommandStatus.Pending);

        await ExecuteCommandManagement(commandToExecute.Id);
        details = await GetPaymentRequestById(paymentRequestId);
        AssertTransactionStatuses(details.Transactions, TransactionStatus.Error, firstStepNumber);
        AssertCommandStatuses(details.PaymentRequestCommands, CommandStatus.Failed, firstStepNumber);
        details.Status.ShouldBe(PaymentRequestStatus.Failed);
    }

    [Theory]
    [InlineData(DomainConstants.InvoicePayment)]
    public async Task Payment_FailedPaymentRequestRetried_SettingTransactionWithErrorStatusShouldNotAffectOthers(string templateCode)
    {
        var message = PaymentRequestData.CreateValidPaymentRequest(templateCode);
        var paymentRequest = await CreateAndAssertPayment(message);

        await ExecuteAndAssertTestCommandCycle(paymentRequest.Id, firstStepNumber, finalStatus: TransactionStatus.Error);

        var failedTransactionId = paymentRequest.Transactions.First(x => x.SequenceNumber == firstStepNumber).Id.ToString();
        await RetryFailedTransaction(paymentRequest.Id.ToString(), failedTransactionId);

        await ExecutePaymentScheduledJob();

        var details = await GetPaymentRequestById(paymentRequest.Id);
        details.Status.ShouldBe(PaymentRequestStatus.Processing);

        foreach (var transaction in details.Transactions)
        {
            if (transaction.Id.ToString() == failedTransactionId)
                continue;

            transaction.Status.ShouldBe(TransactionStatus.Placed);
        }
    }

    [Theory]
    [InlineData(DomainConstants.InvoicePayment)]
    [InlineData(DomainConstants.InvoicePaymentV2)]
    [InlineData(DomainConstants.DrawRepayment)]
    [InlineData(DomainConstants.FinalPayment)]
    [InlineData(DomainConstants.FinalPaymentV2)]
    [InlineData(DomainConstants.FactoringFinalPayment)]
    [InlineData(DomainConstants.InvoiceDisbursementV2)]
    [InlineData(DomainConstants.FactoringDisbursement)]
    [InlineData(DomainConstants.DrawDisbursement)]
    [InlineData(DomainConstants.InvoicePaymentCard)]
    [InlineData(DomainConstants.DrawRepaymentCard)]
    [InlineData(DomainConstants.DrawRepaymentManual)]
    [InlineData(DomainConstants.SubscriptionFeePayment)]
    public async Task Payment_AionReturnsErrorStatus_TransactionStatusFailed(string templateCode)
    {
        var message = PaymentRequestData.CreateValidPaymentRequest(templateCode);
        var paymentRequest = await CreateAndAssertPayment(message);

        await ExecuteAndAssertTestCommandCycle(paymentRequest.Id, firstStepNumber, finalStatus: TransactionStatus.Error);

        var details = await GetPaymentRequestById(paymentRequest.Id);
        details.Status.ShouldBe(PaymentRequestStatus.Failed);
    }

    [Theory]
    [InlineData(DomainConstants.InvoicePayment)]
    [InlineData(DomainConstants.InvoicePaymentV2)]
    [InlineData(DomainConstants.DrawRepayment)]
    [InlineData(DomainConstants.FinalPayment)]
    [InlineData(DomainConstants.FinalPaymentV2)]
    [InlineData(DomainConstants.FactoringFinalPayment)]
    [InlineData(DomainConstants.InvoiceDisbursementV2)]
    [InlineData(DomainConstants.FactoringDisbursement)]
    [InlineData(DomainConstants.DrawDisbursement)]
    [InlineData(DomainConstants.InvoicePaymentCard)]
    [InlineData(DomainConstants.DrawRepaymentCard)]
    [InlineData(DomainConstants.DrawRepaymentManual)]
    [InlineData(DomainConstants.SubscriptionFeePayment)]
    public async Task Payment_AionReturnsException_TransactionStatusFailed(string templateCode)
    {
        var payment = await CreateAndAssertPayment(PaymentRequestData.CreateValidPaymentRequest(templateCode, amount: Configuration.AionExceptionAmount));

        var details = await GetPaymentRequestById(payment.Id);

        details.Status.ShouldBe(PaymentRequestStatus.Requested);

        await ExecutePaymentScheduledJob();
        details = await GetPaymentRequestById(payment.Id);
        AssertTransactionStatuses(details.Transactions, TransactionStatus.Placed, firstStepNumber);
        AssertCommandStatuses(details.PaymentRequestCommands, CommandStatus.Pending, firstStepNumber);
        details.Status.ShouldBe(PaymentRequestStatus.Requested);

        await ReceiveAndExecuteCommandManagementMessages();

        details = await GetPaymentRequestById(payment.Id);
        AssertTransactionStatuses(details.Transactions, TransactionStatus.Failed, firstStepNumber);
        AssertCommandStatuses(details.PaymentRequestCommands, CommandStatus.Failed, firstStepNumber);

        details.Status.ShouldBe(PaymentRequestStatus.Failed);
    }
}
