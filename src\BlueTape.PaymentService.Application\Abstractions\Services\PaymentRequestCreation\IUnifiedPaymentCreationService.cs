﻿using BlueTape.PaymentService.Application.Models;
using BlueTape.PaymentService.Domain.Models.PaymentRequest;

namespace BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation;

public interface IUnifiedPaymentCreationService
{
    Task<PaymentRequestModel> CreatePaymentRequest(CreatePaymentRequestModel createPaymentRequest, string createdBy,
        CancellationToken ct);
}
