using BlueTape.InvoiceService.Models.Invoice;
using BlueTape.LS.DTOs.Loan;
using BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation;
using BlueTape.PaymentService.Application.Abstractions.Validators;
using BlueTape.PaymentService.Application.Models;
using BlueTape.PaymentService.Domain.Constants;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Models.PaymentRequest;

namespace BlueTape.PaymentService.Application.Services.PaymentRequestCreation.Strategies;

/// <summary>
/// Strategy for handling Subscription Fee Payment requests
/// </summary>
public class SubscriptionFeeStrategy : IPaymentCreationStrategy
{
    private readonly IPaymentRequestValidator _paymentRequestValidator;

    public SubscriptionFeeStrategy(IPaymentRequestValidator paymentRequestValidator)
    {
        _paymentRequestValidator = paymentRequestValidator;
    }

    public bool CanHandle(string flowTemplateCode)
    {
        return flowTemplateCode == DomainConstants.SubscriptionFeePayment;
    }

    public async Task ValidatePaymentRequest(CreatePaymentRequestModel createPaymentRequest,
        List<InvoiceModel> existingInvoices, List<PaymentRequestPayableModel> processedPayables,
        LoanDto? loan, CancellationToken ct)
    {
        await _paymentRequestValidator.ValidateSubscriptionFeePaymentRequest(createPaymentRequest, ct);
    }

    public async Task SendNotification(CreatePaymentRequestModel createPaymentRequest,
        PaymentRequestEntity paymentRequestEntity, IEnumerable<InvoiceModel> existingInvoices,
        CancellationToken ct)
    {
        // Subscription fee requests don't send notifications
        await Task.CompletedTask;
    }

    public async Task ProcessAdditionalLogic(CreatePaymentRequestModel createPaymentRequest, CancellationToken ct)
    {
        // No additional processing needed for subscription fees
        await Task.CompletedTask;
    }
}
