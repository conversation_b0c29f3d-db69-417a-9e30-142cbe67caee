﻿using BlueTape.PaymentService.CompatibilityService.Abstractions.Service.InvoicePayment;
using BlueTape.PaymentService.CompatibilityService.Abstractions.Strategies;
using BlueTape.PaymentService.CompatibilityService.Extensions;
using BlueTape.PaymentService.CompatibilityService.Models;
using BlueTape.PaymentService.DataAccess.Mongo.Abstractions.Repositories;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.Operation;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.Transaction;
using BlueTape.PaymentService.Domain.Constants;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Enums.Legacy;
using BlueTape.PaymentService.Domain.Extensions;
using Microsoft.Extensions.Logging;

namespace BlueTape.PaymentService.CompatibilityService.Strategies;

public class InvoicePaymentCompatibilityStrategy(IOperationsRepository operationsRepository,
    ITransactionsRepository transactionsRepository,
    IInvoicePaymentCompatibilityMapper compatibilityMapper,
    IInvoicePaymentOperationsSyncService operationsSyncService,
    IOperationPaymentSyncHelper paymentSyncHelper,
    ILogger<InvoicePaymentCompatibilityStrategy> logger) : ICompatibilityStrategy
{
    public virtual async Task PerformOperation(PaymentRequestEntity paymentRequest, CancellationToken cancellationToken)
    {
        var transactions = new List<TransactionEntity>();
        var payables = paymentRequest.PaymentRequestPayables.ToList();
        var commands = paymentRequest.PaymentRequestCommands.ToList();

        var existingOperationsForPayables =
            await operationsRepository.GetByOwnerIds(payables.Select(x => x.PayableId), null, cancellationToken);
        var existingOperationsToSync = existingOperationsForPayables.Where(x => x.Status != OperationStatus.DECLINED.ToString() && string.IsNullOrEmpty(x.PaymentRequestId)).ToList();

        var invoicesToSyncIds = existingOperationsToSync.Select(x => x.OwnerId).ToList();
        logger.LogInformation("Perform Operation in MongoDB. PaymentRequestId: {paymentRequestId}. Existing operations to sync count: {existingOperationsToSyncCount}",
            paymentRequest.Id, existingOperationsToSync.Count());

        await operationsSyncService.SyncExistingPayablesOperations(existingOperationsToSync, paymentRequest, cancellationToken);

        var operationsToCreate = paymentRequest.PaymentRequestPayables.Where(x => !invoicesToSyncIds.Contains(x.PayableId))
            .Select(payable => compatibilityMapper.MapFromPaymentRequestToOperation(paymentRequest, payable)).ToList();

        logger.LogInformation("Perform Operation in MongoDB. PaymentRequestId: {paymentRequestId}. Operations to create count: {operationsToCreateCount}",
            paymentRequest.Id, operationsToCreate.Count);

        var fees = paymentRequest.PaymentRequestFees.ToList();
        var legacyTransactionsMerchantFees = CalculateLegacyTransactionFeeAmounts(fees, payables, FeeType.Merchant).ToArray();
        var legacyTransactionsPurchaserFees = CalculateLegacyTransactionFeeAmounts(fees, payables, FeeType.Purchaser).ToArray();

        var operationsToCreateTransactions = new List<OperationEntity>();
        operationsToCreateTransactions.AddRange(operationsToCreate);
        operationsToCreateTransactions.AddRange(existingOperationsToSync);

        for (var i = 0; i < operationsToCreateTransactions.Count; i++)
        {
            var operation = operationsToCreateTransactions[i];
            var merchantFee = legacyTransactionsMerchantFees[i];
            var purchaserFee = legacyTransactionsPurchaserFees[i];

            var operationTransactionsTasks = paymentRequest.Transactions
                .Where(x => x.TransactionType is PaymentTransactionType.AchPull || x.TransactionType.IsPushTransaction()).Select(transaction =>
            {
                var commandStepName = commands.Find(x => x.TransactionId == transaction.Id)?.StepName;
                var transactionType = commandStepName.GetLegacyTransactionType();
                var legacyTransactionAmount = CalculateLegacyTransactionAmount(payables, operation, merchantFee, transactionType);

                var createLegacyTransactionModel = new CreateLegacyTransactionModel()
                {
                    OperationId = operation.BlueTapeId,
                    PayeeId = paymentRequest.PayeeId,
                    PayerId = paymentRequest.PayerId,
                    Transaction = transaction,
                    MerchantFeeAmount = merchantFee,
                    PurchaserFeeAmount = purchaserFee,
                    LegacyTransactionAmount = legacyTransactionAmount,
                    TransactionType = transactionType,
                    Provider = transaction.Provider.ToString(),
                    PublicTransactionNumber = transaction.PublicTransactionNumber
                };

                return compatibilityMapper.MapFromPaymentTransactionToLegacyTransaction(createLegacyTransactionModel, cancellationToken);
            }).ToList();

            await Task.WhenAll(operationTransactionsTasks);
            var operationTransactions = operationTransactionsTasks.Select(x => x.Result);

            transactions.AddRange(operationTransactions);
        }

        logger.LogInformation("Perform Operation in MongoDB. PaymentRequestId: {paymentRequestId}. Transactions generated count: {transactionsCount}",
            paymentRequest.Id, transactions.Count);

        await operationsRepository.InsertMany(operationsToCreate, cancellationToken);
        await transactionsRepository.InsertMany(transactions, cancellationToken);
    }

    public Task SyncOperation(PaymentRequestEntity paymentRequest, CancellationToken cancellationToken)
    {
        return operationsSyncService.SyncOperation(paymentRequest, cancellationToken);
    }

    public virtual bool IsApplicable(string templateCode)
    {
        return templateCode is DomainConstants.InvoicePayment;
    }

    protected static IEnumerable<decimal> CalculateLegacyTransactionFeeAmounts(List<PaymentRequestFeeEntity> fees, List<PaymentRequestPayableEntity> payables, FeeType type)
    {
        var results = new List<decimal>();
        var fee = fees.Find(x => x.Type == type)?.Amount ?? 0;

        for (var currentCount = payables.Count; currentCount >= 1; currentCount--)
        {
            var result = Math.Round(fee / currentCount, 2);
            fee -= result;

            results.Add(result);
        }

        return results.OrderBy(amount => amount);
    }

    protected static decimal CalculateLegacyTransactionAmount(List<PaymentRequestPayableEntity> payables, OperationEntity operation, decimal merchantFee, LegacyTransactionType? transactionType)
    {
        var payable = payables.First(x => x.PayableId == operation.OwnerId);
        var result = payable.Amount - payable.Discount;

        if (transactionType == LegacyTransactionType.OUT)
        {
            result -= merchantFee;
        }

        return Math.Round(result, 2);
    }
}