﻿using BlueTape.AionServiceClient.Exceptions;
using BlueTape.Common.ExceptionHandling.Exceptions;
using BlueTape.Integrations.Aion;
using BlueTape.Integrations.Aion.Ach.CreateAchTransfer;
using BlueTape.Integrations.Aion.AzureTableStorage.Abstractions;
using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BlueTape.PaymentService.Application.Abstractions.Mappers;
using BlueTape.PaymentService.Application.Abstractions.Services;
using BlueTape.PaymentService.Application.Abstractions.Services.External;
using BlueTape.PaymentService.Application.Abstractions.Services.Notification;
using BlueTape.PaymentService.Application.Constants;
using BlueTape.PaymentService.Application.Handlers.Commands.PaymentCommands;
using BlueTape.PaymentService.Application.Models;
using BlueTape.PaymentService.Application.Models.Commands;
using BlueTape.PaymentService.Application.Tests.Constants;
using BlueTape.PaymentService.Application.Tests.Models;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.PaymentFlowTemplatesEngine.Models.Raw;
using BlueTape.PaymentService.UnitOfWork.Abstractions;
using BlueTape.Utilities.Providers;
using MediatR;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NSubstitute.ExceptionExtensions;
using System.Linq.Expressions;

namespace BlueTape.PaymentService.Application.Tests.Handlers;

public class PushToMerchantFullAmountTransactionHandlerTests
{
    private readonly PushToMerchantFullAmountTransactionHandler _handler;

    private readonly IAionServiceV2 _aionService = Substitute.For<IAionServiceV2>();
    private readonly ILogger<PushToMerchantFullAmountTransactionHandler> _logger = Substitute.For<ILogger<PushToMerchantFullAmountTransactionHandler>>();
    private readonly IDateProvider _dateProvider = Substitute.For<IDateProvider>();
    private readonly ITransactionNumberService _transactionNumberService = Substitute.For<ITransactionNumberService>();
    private readonly IUnitOfWork _unitOfWork = Substitute.For<IUnitOfWork>();
    private readonly IPaymentRequestService _paymentRequestService = Substitute.For<IPaymentRequestService>();

    private readonly IMediator _mediator = Substitute.For<IMediator>();
    private readonly ISlackNotificationService _slackNotificationService = Substitute.For<ISlackNotificationService>();
    private readonly IStatusService _statusService = Substitute.For<IStatusService>();
    private readonly IAzureStorageTransactionRepository _azureStorageTransactionRepository = Substitute.For<IAzureStorageTransactionRepository>();
    private readonly IOptions<RawFlowTemplateOptions> _flowTemplateOptions = Substitute.For<IOptions<RawFlowTemplateOptions>>();
    private readonly IPaymentConfigService _paymentConfigService = Substitute.For<IPaymentConfigService>();

    public PushToMerchantFullAmountTransactionHandlerTests()
    {
        var rawFlowTemplateOptions = RawFlowTemplates.RawFlowTemplateOptions;

        _flowTemplateOptions.Value
            .Returns(rawFlowTemplateOptions);

        _handler = new PushToMerchantFullAmountTransactionHandler(
            _aionService,
            _dateProvider,
            _logger,
            _transactionNumberService,
            _unitOfWork,
            _paymentRequestService,
            _mediator,
            _slackNotificationService,
            _statusService,
            _azureStorageTransactionRepository,
            _flowTemplateOptions,
            _paymentConfigService);
    }

    [Theory, AutoFixtureCustom]
    public Task Handle_CommandIsNotFound_ThrowsVariableNullException(PushToMerchantFullAmountCommand request)
    {
        _unitOfWork.GetById<PaymentRequestCommandEntity>(request.PaymentRequestCommandId, default,
            HandlersConstants.ExecutionHistorySearchProperties).Returns((PaymentRequestCommandEntity?)null);

        return _handler.Handle(request, default).ShouldThrowAsync<VariableNullException>();
    }

    [Theory, AutoFixtureCustom]
    public async Task Handle_CommandStatusIsNotPending_StopsTheExecution(PushToMerchantFullAmountCommand request,
        PaymentRequestCommandEntity command)
    {
        command.Status = CommandStatus.Placed;

        _unitOfWork.GetById<PaymentRequestCommandEntity>(request.PaymentRequestCommandId, default,
            HandlersConstants.ExecutionHistorySearchProperties).Returns(command);

        await _handler.Handle(request, default);

        _aionService
            .ReceivedCalls().Count().ShouldBe(0);
    }

    [Theory, AutoFixtureCustom]
    public async Task Handle_TransactionAmountIsZero_MarksCommandAsExecuted(PushToMerchantFullAmountCommand request,
        PaymentRequestCommandEntity command, PaymentTransactionEntity transaction, PaymentRequestCommandEntity executedCommand)
    {
        transaction.Amount = 0;
        command.Transaction = transaction;
        command.Status = CommandStatus.Pending;
        var executedAt = DateTime.UtcNow;
        executedCommand.Status = CommandStatus.Executed;

        _dateProvider.CurrentDateTime.Returns(executedAt);
        _unitOfWork.GetById<PaymentRequestCommandEntity>(request.PaymentRequestCommandId, default,
            HandlersConstants.ExecutionHistorySearchProperties).Returns(command);
        _paymentRequestService.MarkCommandAsExecuted(command.Id, nameof(command), default).Returns(executedCommand);

        await _handler.Handle(request, default);

        await _aionService
            .DidNotReceive().CreateExternalTransfer(Arg.Any<CreateAchModel>(), Arg.Any<Guid>(), Arg.Any<PaymentSubscriptionType>(), Arg.Any<TransactionType>(), Arg.Any<AionPaymentMethodType>(), default);

        await _paymentRequestService.Received().CommitPaymentRequestChanges(executedCommand.PaymentRequestId,
            $"{ApplicationConstants.ScheduledEventProcessor}:{nameof(PushToMerchantFullAmountTransactionHandler)}", default);
    }

    [Theory, AutoFixtureCustom]
    public async Task Handle_NotEnoughAvailableBalance_NotExecutesTransaction(PushToMerchantFullAmountCommand request,
        PaymentRequestCommandEntity command, PaymentTransactionEntity transaction, BlueTapeTransactionResponseModel response)
    {
        transaction.OriginatorAccountId = "BT-COLLECTION";
        transaction.TransactionNumber = null;
        transaction.Provider = PaymentProvider.Aion;
        transaction.TransactionType = PaymentTransactionType.AchInternal;
        transaction.ReceiverAccountId = "2255-FUNDING";
        command.Transaction = transaction;
        command.Status = CommandStatus.Pending;
        response.AionResponse = "test response";
        response.AionReferenceId = "test-ref-id";
        var executedAt = DateTime.UtcNow;

        _dateProvider.CurrentDateTime.Returns(executedAt);
        _unitOfWork.GetById<PaymentRequestCommandEntity>(request.PaymentRequestCommandId, default,
            HandlersConstants.ExecutionHistorySearchProperties).Returns(command);
        _aionService.GetAccountBalance(AccountCodeType.FUNDING, command.PaymentRequestId, Arg.Any<PaymentSubscriptionType>(), default).Returns(0);
        _paymentRequestService.GetById(Arg.Any<Guid>(), default).Returns(new PaymentRequestModel());

        await _handler.Handle(request, default);

        await _aionService
            .DidNotReceive().CreateExternalTransfer(Arg.Any<CreateAchModel>(), Arg.Any<Guid>(), Arg.Any<PaymentSubscriptionType>(), Arg.Any<TransactionType>(), Arg.Any<AionPaymentMethodType>(), default);

        await _paymentRequestService.CommitPaymentRequestChanges(command.PaymentRequestId,
            $"{ApplicationConstants.ScheduledEventProcessor}:{nameof(PushToMerchantFullAmountTransactionHandler)}", default);
    }

    [Theory, AutoFixtureCustom]
    public async Task Handle_AionServiceThrowsTooManyRequestsException_RollbacksChanges(PushToMerchantFullAmountCommand request,
        PaymentRequestCommandEntity command, PaymentTransactionEntity transaction, PaymentRequestModel paymentRequestModel)
    {
        transaction.ReceiverAccountId = "BT-FUNDING";
        transaction.TransactionNumber = null;
        command.PaymentRequest = PaymentRequests.PayNowRequestWithCustomFlowTemplateCode(
            RawFlowTemplates.RawFlowTemplateOptions.Templates.First().FlowTemplateCode ?? "");
        command.Transaction = transaction;
        command.Status = CommandStatus.Pending;

        _unitOfWork.GetById<PaymentRequestCommandEntity>(request.PaymentRequestCommandId, default,
            HandlersConstants.ExecutionHistorySearchProperties).Returns(command);
        _transactionNumberService.GetUniqueTransactionNumber(default).Returns(HandlersConstants.TransactionNumber);
        _paymentRequestService.GetById(Arg.Any<Guid>(), default).Returns(paymentRequestModel);
        _aionService.CreateExternalTransfer(Arg.Is<CreateAchModel>(x =>
                x.Amount == transaction.Amount &&
                x.TransactionNumber == HandlersConstants.TransactionNumber &&
                x.Description == transaction.PublicTransactionNumber &&
                x.OriginatorAccountCode == AccountCodeType.FUNDING),
            command.PaymentRequestId, Arg.Any<PaymentSubscriptionType>(), TransactionType.Push, Arg.Any<AionPaymentMethodType>(), default)
            .ThrowsAsync<TooManyRequestsException>();
        _unitOfWork.Get<PaymentTransactionHistoryEntity>(default, x =>
            x.ResultCode != null && x.ResultCode.Equals(BlueTapePaymentErrorCodes.BT002.ToString()) && x.TransactionId == command.TransactionId)
            .Returns(new List<PaymentTransactionHistoryEntity>());

        await _handler.Handle(request, default).ShouldNotThrowAsync();

        await _unitOfWork.PaymentRequestCommandRepository.Received().Update(Arg.Is<PaymentRequestCommandEntity>(x =>
                x.Status == CommandStatus.Placed &&
                x.UpdatedBy == $"{ApplicationConstants.ScheduledEventProcessor}:{nameof(PushToMerchantFullAmountTransactionHandler)}"),
                default);

        _aionService
            .ReceivedCalls().Count().ShouldBe(1);

        await _paymentRequestService.CommitPaymentRequestChanges(command.PaymentRequestId,
            $"{ApplicationConstants.ScheduledEventProcessor}:{nameof(PushToMerchantFullAmountTransactionHandler)}", default);
    }

    [Theory, AutoFixtureCustom]
    public async Task Handle_NotEnoughBalance_InsertsNewTransactionHistory(CollectToFundingCommand request,
        PaymentRequestCommandEntity command, PaymentTransactionEntity transaction, PaymentRequestModel paymentRequestModel)
    {
        transaction.ReceiverAccountId = "BT-COLLECTIONREPAYMENT";
        transaction.TransactionNumber = null;
        command.PaymentRequest = PaymentRequests.PayNowRequestWithCustomFlowTemplateCode(
            RawFlowTemplates.RawFlowTemplateOptions.Templates.First().FlowTemplateCode ?? "");
        command.Transaction = transaction;
        command.Status = CommandStatus.Pending;
        command.StepName = StepName.PushToMerchantFullAmount.ToString();

        _unitOfWork.GetById<PaymentRequestCommandEntity>(request.PaymentRequestCommandId, default,
            HandlersConstants.ExecutionHistorySearchProperties).Returns(command);
        _transactionNumberService.GetUniqueTransactionNumber(default).Returns(HandlersConstants.TransactionNumber);
        _aionService.CreateExternalTransfer(Arg.Is<CreateAchModel>(x =>
                x.Amount == transaction.Amount &&
                x.TransactionNumber == HandlersConstants.TransactionNumber &&
                x.Description == transaction.PublicTransactionNumber &&
                x.OriginatorAccountCode == AccountCodeType.FUNDING),
            command.PaymentRequestId, Arg.Any<PaymentSubscriptionType>(), TransactionType.Push, Arg.Any<AionPaymentMethodType>(), default)
            .ThrowsAsync<TooManyRequestsException>();
        _paymentRequestService.GetById(Arg.Any<Guid>(), default).Returns(paymentRequestModel);
        _unitOfWork.Get(default, Arg.Any<Expression<Func<PaymentTransactionHistoryEntity, bool>>>())
            .Returns(PaymentTransactionHistoryList.PaymentTransactionHistory(command.TransactionId));

        await _handler.Handle(request, default).ShouldNotThrowAsync();

        await _unitOfWork.PaymentTransactionHistoryRepository.Received().Insert(Arg.Is<PaymentTransactionHistoryEntity>(x =>
            x.NewStatus == TransactionStatus.Error
            && x.ResultCode == BlueTapePaymentErrorCodes.BT003.ToString()), default);

        await _paymentRequestService.CommitPaymentRequestChanges(command.PaymentRequestId,
            $"{ApplicationConstants.ScheduledEventProcessor}:{nameof(PushToMerchantFullAmountTransactionHandler)}", default);
    }

    [Theory, AutoFixtureCustom]
    public async Task Handle_AionServiceThrowsException_ThrowsException(PushToMerchantFullAmountCommand request,
        PaymentRequestCommandEntity command, PaymentTransactionEntity transaction)
    {
        transaction.TransactionType = PaymentTransactionType.AchPull;
        transaction.ReceiverAccountId = "BT-FUNDING";
        transaction.TransactionNumber = null;
        command.Transaction = transaction;
        command.Status = CommandStatus.Pending;

        _unitOfWork.GetById<PaymentRequestCommandEntity>(request.PaymentRequestCommandId, default,
            HandlersConstants.ExecutionHistorySearchProperties).Returns(command);
        _transactionNumberService.GetUniqueTransactionNumber(default).Returns(HandlersConstants.TransactionNumber);
        _aionService.CreateExternalTransfer(Arg.Is<CreateAchModel>(x =>
                x.Amount == transaction.Amount &&
                x.TransactionNumber == HandlersConstants.TransactionNumber &&
                x.Description == transaction.PublicTransactionNumber &&
                x.OriginatorAccountCode == AccountCodeType.FUNDING),
            command.PaymentRequestId, Arg.Any<PaymentSubscriptionType>(), TransactionType.Push, Arg.Any<AionPaymentMethodType>(), default)
            .ThrowsAsync<AionResponseException>();
        _paymentRequestService.GetById(Arg.Any<Guid>(), default).Returns(new PaymentRequestModel());

        await _handler.Handle(request, default).ShouldNotThrowAsync();

        await _unitOfWork.PaymentTransactionRepository.Received().Update(Arg.Is<PaymentTransactionEntity>(x =>
                x.Status == TransactionStatus.Failed &&
                x.UpdatedBy == $"{ApplicationConstants.ScheduledEventProcessor}:{nameof(PushToMerchantFullAmountTransactionHandler)}"),
                default);
        await _unitOfWork.PaymentRequestCommandRepository.Received().Update(Arg.Is<PaymentRequestCommandEntity>(x =>
                x.Status == CommandStatus.Failed &&
                x.UpdatedBy == $"{ApplicationConstants.ScheduledEventProcessor}:{nameof(PushToMerchantFullAmountTransactionHandler)}"),
                default);
        await _unitOfWork.PaymentTransactionHistoryRepository.Received().Insert(Arg.Is<PaymentTransactionHistoryEntity>(x =>
                x.NewStatus == TransactionStatus.Failed &&
                x.CreatedBy == $"{ApplicationConstants.ScheduledEventProcessor}:{nameof(PushToMerchantFullAmountTransactionHandler)}"),
                default);

        _slackNotificationService.ReceivedCalls().Count().ShouldBe(1);

        await _paymentRequestService.CommitPaymentRequestChanges(command.PaymentRequestId,
            $"{ApplicationConstants.ScheduledEventProcessor}:{nameof(PushToMerchantFullAmountTransactionHandler)}", default);
    }

    [Theory, AutoFixtureCustom]
    public async Task Handle_SuccessfulFlow_NotThrowsException(PushToMerchantFullAmountCommand request,
        PaymentRequestCommandEntity command, PaymentTransactionEntity transaction, BlueTapeTransactionResponseModel response)
    {
        var initialTransactionStatus = transaction.Status;

        transaction.TransactionType = PaymentTransactionType.AchPush;
        transaction.ReceiverAccountId = "BT-FUNDING";
        transaction.TransactionNumber = null;
        command.Transaction = transaction;
        command.Status = CommandStatus.Pending;
        response.AionResponse = "test response";
        response.AionReferenceId = "test-ref-id";
        var executedAt = DateTime.UtcNow;

        _dateProvider.CurrentDateTime.Returns(executedAt);

        _unitOfWork.GetById<PaymentRequestCommandEntity>(request.PaymentRequestCommandId, default,
            HandlersConstants.ExecutionHistorySearchProperties).Returns(command);
        _transactionNumberService.GetUniqueTransactionNumber(default).Returns(HandlersConstants.TransactionNumber);
        _aionService.CreateExternalTransfer(Arg.Is<CreateAchModel>(x =>
                x.Amount == transaction.Amount &&
                x.TransactionNumber == HandlersConstants.TransactionNumber &&
                x.Description == transaction.PublicTransactionNumber &&
                x.OriginatorAccountCode == AccountCodeType.FUNDING),
            command.PaymentRequestId, Arg.Any<PaymentSubscriptionType>(), TransactionType.Push, Arg.Any<AionPaymentMethodType>(), default)
            .Returns(response);
        _paymentRequestService.GetById(Arg.Any<Guid>(), default).Returns(new PaymentRequestModel());

        await _handler.Handle(request, default);

        await _unitOfWork.PaymentTransactionRepository.Received().Update(Arg.Is<PaymentTransactionEntity>(x =>
                x.Status == TransactionStatus.Processing &&
                x.UpdatedBy == $"{ApplicationConstants.ScheduledEventProcessor}:{nameof(PushToMerchantFullAmountTransactionHandler)}" &&
                x.ExecutedAt == executedAt),
                default);
        await _unitOfWork.PaymentRequestCommandRepository.Received().Update(Arg.Is<PaymentRequestCommandEntity>(x =>
                x.Status == CommandStatus.Executing &&
                x.UpdatedBy == $"{ApplicationConstants.ScheduledEventProcessor}:{nameof(PushToMerchantFullAmountTransactionHandler)}"),
                default);
        await _unitOfWork.PaymentTransactionHistoryRepository.Received().Insert(Arg.Is<PaymentTransactionHistoryEntity>(x =>
                x.NewStatus == TransactionStatus.Processing &&
                x.CreatedBy == $"{ApplicationConstants.ScheduledEventProcessor}:{nameof(PushToMerchantFullAmountTransactionHandler)}"),
                default);

        await _paymentRequestService.CommitPaymentRequestChanges(command.PaymentRequestId,
            $"{ApplicationConstants.ScheduledEventProcessor}:{nameof(PushToMerchantFullAmountTransactionHandler)}", default);
    }
}
