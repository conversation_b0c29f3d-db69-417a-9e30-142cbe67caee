using System.Text.Json.Serialization;

namespace BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.IhcRepayment;

public class IhcRepaymentRequestDetails
{
    [JsonPropertyName("date")]
    public DateTime Date { get; set; }

    [JsonPropertyName("currency")]
    public string Currency { get; set; } = string.Empty;

    [JsonPropertyName("requestedAmount")]
    public decimal RequestedAmount { get; set; }

    [JsonPropertyName("paymentMethod")]
    public string PaymentMethod { get; set; } = string.Empty;

    [JsonPropertyName("drawDetails")]
    public IhcRepaymentDrawDetails DrawDetails { get; set; } = new();

    [JsonPropertyName("customerDetails")]
    public IhcRepaymentCustomerDetails CustomerDetails { get; set; } = new();
}
