using BlueTape.InvoiceService.Models.Invoice;
using BlueTape.LS.DTOs.Loan;
using BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation;
using BlueTape.PaymentService.Application.Abstractions.Validators;
using BlueTape.PaymentService.Application.Models;
using BlueTape.PaymentService.DataAccess.Mongo.Abstractions.Repositories;
using BlueTape.PaymentService.Domain.Constants;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Models.PaymentRequest;
using BlueTape.PaymentService.PaymentFlowTemplatesEngine.Abstractions;
using BlueTape.Reporting.Application.Abstractions.LMS;

namespace BlueTape.PaymentService.Application.Services.PaymentRequestCreation.Strategies;

/// <summary>
/// Strategy for handling Factoring requests (Disbursement and Final Payment)
/// </summary>
public class FactoringDisbursementStrategy : IPaymentCreationStrategy
{
    private readonly IPaymentRequestValidator _paymentRequestValidator;
    private readonly IPaymentFlowTemplatesEngine _templatesEngine;
    private readonly IDrawApprovalRepository _drawApprovalRepository;
    private readonly ILoanReportingService _loanReportingService;

    public FactoringDisbursementStrategy(
        IPaymentRequestValidator paymentRequestValidator,
        IPaymentFlowTemplatesEngine templatesEngine,
        IDrawApprovalRepository drawApprovalRepository,
        ILoanReportingService loanReportingService)
    {
        _paymentRequestValidator = paymentRequestValidator;
        _templatesEngine = templatesEngine;
        _drawApprovalRepository = drawApprovalRepository;
        _loanReportingService = loanReportingService;
    }

    public bool CanHandle(string flowTemplateCode)
    {
        return flowTemplateCode is
            DomainConstants.FactoringDisbursement or
            DomainConstants.FactoringFinalPayment;
    }

    public async Task ValidatePaymentRequest(CreatePaymentRequestModel createPaymentRequest,
        List<InvoiceModel> existingInvoices, List<PaymentRequestPayableModel> processedPayables,
        LoanDto? loan, CancellationToken ct)
    {
        // Both factoring disbursement and final payment use the same validation
        await _paymentRequestValidator.ValidateSupplierDisbursementRequest(
            createPaymentRequest, existingInvoices, processedPayables, ct);
    }

    public async Task SendNotification(CreatePaymentRequestModel createPaymentRequest,
        PaymentRequestEntity paymentRequestEntity, IEnumerable<InvoiceModel> existingInvoices,
        CancellationToken ct)
    {
        // Factoring requests don't send notifications (ToDo: ledger, notifications)
        await Task.CompletedTask;
    }

    public async Task ProcessAdditionalLogic(CreatePaymentRequestModel createPaymentRequest, CancellationToken ct)
    {
        // Set delay for factoring disbursement
        if (createPaymentRequest.FlowTemplateCode == DomainConstants.FactoringDisbursement)
        {
            createPaymentRequest.MerchantAchDelayInBusinessDays =
                _templatesEngine.GetPaymentRequestDelay(createPaymentRequest.FlowTemplateCode);
        }

        // Find and set DrawId from draw approval
        var drawApproval = await _drawApprovalRepository.GetByInvoicesIds(
            createPaymentRequest.PaymentRequestPayables.Select(x => x.Id).ToArray(), ct);

        if (drawApproval != null)
        {
            var loan = (await _loanReportingService.GetLoansByDrawApprovalIdsAsync([drawApproval.Id], ct))
                .FirstOrDefault();

            if (loan != null)
                createPaymentRequest.DrawId = loan.Id;
        }
    }
}
