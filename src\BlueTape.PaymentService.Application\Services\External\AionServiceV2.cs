using BlueTape.AionServiceClient.Abstractions;
using BlueTape.Integrations.Aion;
using BlueTape.Integrations.Aion.Accounts;
using BlueTape.Integrations.Aion.Ach.CreateAchTransfer;
using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BlueTape.Integrations.Aion.Internal;
using BlueTape.Integrations.Aion.Transactions;
using BlueTape.PaymentService.Application.Abstractions.Services.External;

using BlueTape.Utilities.Models;
using Microsoft.Extensions.Caching.Memory;

namespace BlueTape.PaymentService.Application.Services.External;

public class AionServiceV2(
    IAionHttpClient aionExternalService,
    IMemoryCache memoryCache) : IAionServiceV2
{
    private const string CacheKey = "GetAccountsCacheKey";
    private static readonly TimeSpan CacheDuration = TimeSpan.FromSeconds(15);

    public Task<BlueTapeTransactionResponseModel?> CreateExternalTransfer(
        CreateAchModel requestModel, Guid paymentRequestId, PaymentSubscriptionType paymentSubscription,
        TransactionType transactionType, AionPaymentMethodType paymentMethod, CancellationToken ct)
    {
        return aionExternalService.CreateExternalTransfer(requestModel, paymentRequestId, paymentSubscription, transactionType, paymentMethod, ct);
    }

    public Task<BlueTapeTransactionResponseModel?> CreateInternalTransfer(CreateInternalModel requestModel, Guid paymentRequestId, PaymentSubscriptionType paymentSubscription, CancellationToken ct)
    {
        return aionExternalService.CreateInternalTransferV2(requestModel, paymentRequestId, paymentSubscription, ct);
    }

    public Task<decimal> GetAccountBalance(AccountCodeType accountCodeType, Guid paymentRequestId, PaymentSubscriptionType paymentSubscription, CancellationToken ct)
        => aionExternalService.GetAccountBalance(accountCodeType, paymentRequestId.ToString(), paymentSubscription, ct);

    public Task<PaginatedResponse<TransactionListObj>?> GetAionAccountTransactions(TransactionsQuery query, CancellationToken ct)
        => aionExternalService.GetAionAccountTransactions(query, ct);

    public async Task<List<AccountResponseObj?>?> GetAccounts(CancellationToken cancellationToken)
    {
        if (!memoryCache.TryGetValue(CacheKey, out List<AccountResponseObj?>? cachedAccounts))
        {
            cachedAccounts = await aionExternalService.GetAccounts(cancellationToken);

            memoryCache.Set(CacheKey, cachedAccounts, CacheDuration);
        }

        return cachedAccounts;
    }
}
