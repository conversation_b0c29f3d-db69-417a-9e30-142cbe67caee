﻿using BlueTape.Integrations.Aion.AzureTableStorage.Abstractions;
using BlueTape.Integrations.Aion.Internal;
using BlueTape.PaymentService.Application.Abstractions.Mappers;
using BlueTape.PaymentService.Application.Abstractions.Services;
using BlueTape.PaymentService.Application.Abstractions.Services.External;
using BlueTape.PaymentService.Application.Abstractions.Services.Notification;
using BlueTape.PaymentService.Application.Constants;
using BlueTape.PaymentService.Application.Mappers;
using BlueTape.PaymentService.Application.Models.Commands;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.PaymentFlowTemplatesEngine.Models.Raw;
using BlueTape.PaymentService.UnitOfWork.Abstractions;
using BlueTape.Utilities.Providers;
using MediatR;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace BlueTape.PaymentService.Application.Handlers.Commands.PaymentCommands;

public class RevenueToCollectTransactionHandler(
    IAionServiceV2 aionService,
    IDateProvider dateProvider,
    ILogger<RevenueToCollectTransactionHandler> logger,
    ITransactionNumberService transactionNumberService,
    IUnitOfWork unitOfWork,
    IPaymentRequestService paymentRequestService,
    IMediator mediator,
    ISlackNotificationService slackNotificationService,
    IStatusService statusService,
    IAzureStorageTransactionRepository azureStorageTransactionRepository,
    IOptions<RawFlowTemplateOptions> options,
    IPaymentConfigService paymentConfigService)
    : AionBaseHandler(logger,
        transactionNumberService,
        unitOfWork,
        dateProvider,
        paymentRequestService,
        aionService,
        mediator,
        slackNotificationService,
        statusService,
        azureStorageTransactionRepository,
        options,
        paymentConfigService), IRequestHandler<RevenueToCollectCommand>
{
    private readonly IAionServiceV2 _aionService = aionService;

    public Task Handle(RevenueToCollectCommand request, CancellationToken cancellationToken)
    {
        return base.Handle(request, cancellationToken);
    }

    protected override async Task<PaymentRequestCommandEntity> ProcessTransactionTransfer(PaymentRequestCommandEntity command, CancellationToken ct)
    {
        var paymentRequest = command.PaymentRequest ?? throw new ArgumentNullException(nameof(command.PaymentRequest), "Payment request cannot be null.");
        var transaction = command.Transaction ?? throw new ArgumentNullException(nameof(command.Transaction), "Transaction cannot be null.");

        var internalTransferModel = new CreateInternalModel()
        {
            Amount = transaction.Amount,
            Receiver = new BaseInternalAccountDetailsModel()
            {
                Name = ApplicationConstants.BlueTape,
                Description = ApplicationConstants.BlueTape,
                AccountCode = transaction.ReceiverAccountId.MapAccountCodeToEnum()
            },
            Originator = new BaseInternalAccountDetailsModel()
            {
                Name = ApplicationConstants.BlueTape,
                Description = ApplicationConstants.BlueTape,
                AccountCode = transaction.OriginatorAccountId.MapAccountCodeToEnum()
            },
            TransactionNumber = transaction.TransactionNumber!,
            TransactionId = transaction.Id.ToString(),
            Description = ApplicationConstants.BlueTape
        };

        logger.LogInformation("Started a revenue to collect transaction {id}, paymentRequestId: {paymentRequestId}", transaction.Id, command.PaymentRequestId);

        var result = await _aionService.CreateInternalTransfer(internalTransferModel, command.PaymentRequestId, paymentRequest.PaymentSubscription, ct);

        logger.LogInformation("Finished a revenue to collect transaction {id}, paymentRequestId: {paymentRequestId}", transaction.Id, command.PaymentRequestId);

        command.Transaction.MetaData = result?.AionResponse ?? string.Empty;

        if (!string.IsNullOrEmpty(result.AionReferenceId))
            command.Transaction.ReferenceNumber = result.AionReferenceId;

        command.Transaction.ProcessingFees = result.AionFee;

        return command;
    }

    protected override string GetHandlerName()
    {
        return $"{ApplicationConstants.ScheduledEventProcessor}:{nameof(RevenueToCollectTransactionHandler)}";
    }
}
