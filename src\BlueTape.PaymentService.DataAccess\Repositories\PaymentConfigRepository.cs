using BlueTape.PaymentService.DataAccess.Abstractions.Repositories;
using BlueTape.PaymentService.DataAccess.Contexts;
using BlueTape.PaymentService.DataAccess.Repositories.Base;
using BlueTape.PaymentService.Domain.Entities;
using Microsoft.EntityFrameworkCore;

namespace BlueTape.PaymentService.DataAccess.Repositories;

public class PaymentConfigRepository(DatabaseContext context) 
    : GenericRepository<PaymentConfigEntity>(context), IPaymentConfigRepository
{
    public async Task<PaymentConfigEntity?> GetByConfigKey(string configKey, CancellationToken ct)
    {
        return await DbSet!.FirstOrDefaultAsync(x => x.ConfigKey == configKey, ct);
    }
    
    public async Task<List<PaymentConfigEntity>> GetByConfigKeys(IEnumerable<string> configKeys, CancellationToken ct)
    {
        return await DbSet!.Where(x => configKeys.Contains(x.Config<PERSON>)).ToListAsync(ct);
    }
}
