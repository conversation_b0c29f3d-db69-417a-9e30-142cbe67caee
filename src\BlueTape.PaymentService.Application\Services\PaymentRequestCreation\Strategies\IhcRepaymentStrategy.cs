using BlueTape.InvoiceService.Models.Invoice;
using BlueTape.LS.DTOs.Loan;
using BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation;
using BlueTape.PaymentService.Application.Abstractions.Validators;
using BlueTape.PaymentService.Application.Models;
using BlueTape.PaymentService.DataAccess.External.Models;
using BlueTape.PaymentService.Domain.Constants;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Models.PaymentRequest;

namespace BlueTape.PaymentService.Application.Services.PaymentRequestCreation.Strategies;

/// <summary>
/// Strategy for handling IHC Repayment requests (both ACH and Card)
/// This replaces the ManualPaymentPull template and supports LMS-initiated payment requests
/// </summary>
public class IhcRepaymentStrategy : IPaymentCreationStrategy
{
    private readonly IPaymentRequestValidator _paymentRequestValidator;

    public IhcRepaymentStrategy(IPaymentRequestValidator paymentRequestValidator)
    {
        _paymentRequestValidator = paymentRequestValidator;
    }

    public bool CanHandle(string flowTemplateCode)
    {
        return flowTemplateCode == DomainConstants.IhcRepayment;
    }

    public async Task ValidatePaymentRequest(CreatePaymentRequestModel createPaymentRequest,
        List<InvoiceModel> existingInvoices, List<PaymentRequestPayableModel> processedPayables, 
        LoanDto? loan, CancellationToken ct)
    {
        // Use the same validation as Draw Repayment since IHC Repayment is similar to draw repayment
        await _paymentRequestValidator.ValidateDrawRepaymentPaymentRequest(createPaymentRequest, loan, ct);
    }

    public async Task SendNotification(CreatePaymentRequestModel createPaymentRequest, 
        PaymentRequestEntity paymentRequestEntity, IEnumerable<InvoiceModel> existingInvoices, 
        CancellationToken ct)
    {
        // IHC repayment requests don't send notifications (similar to draw repayment)
        // The LMS will handle its own notifications based on the payment results
        await Task.CompletedTask;
    }

    public async Task ProcessAdditionalLogic(CreatePaymentRequestModel createPaymentRequest, CancellationToken ct)
    {
        // No additional processing needed for IHC repayments
        // The payment method (ACH or Card) is already set in the request
        await Task.CompletedTask;
    }
}
