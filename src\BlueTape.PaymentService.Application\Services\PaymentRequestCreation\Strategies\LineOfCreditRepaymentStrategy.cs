using BlueTape.InvoiceService.Models.Invoice;
using BlueTape.LS.DTOs.Loan;
using BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation;
using BlueTape.PaymentService.Application.Abstractions.Validators;
using BlueTape.PaymentService.Application.Models;
using BlueTape.PaymentService.Domain.Constants;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Models.PaymentRequest;

namespace BlueTape.PaymentService.Application.Services.PaymentRequestCreation.Strategies;

/// <summary>
/// Strategy for handling Draw Repayment requests (ACH, Card, Manual)
/// </summary>
public class LineOfCreditRepaymentStrategy : IPaymentCreationStrategy
{
    private readonly IPaymentRequestValidator _paymentRequestValidator;

    public LineOfCreditRepaymentStrategy(IPaymentRequestValidator paymentRequestValidator)
    {
        _paymentRequestValidator = paymentRequestValidator;
    }

    public bool CanHandle(string flowTemplateCode)
    {
        return flowTemplateCode is
            DomainConstants.DrawRepayment or
            DomainConstants.DrawRepaymentCard or
            DomainConstants.DrawRepaymentManual;
    }

    public async Task ValidatePaymentRequest(CreatePaymentRequestModel createPaymentRequest,
        List<InvoiceModel> existingInvoices, List<PaymentRequestPayableModel> processedPayables,
        LoanDto? loan, CancellationToken ct)
    {
        if (createPaymentRequest.FlowTemplateCode == DomainConstants.DrawRepaymentManual)
        {
            _paymentRequestValidator.ValidateManualPaymentRequest(createPaymentRequest, loan, ct);
        }
        else
        {
            await _paymentRequestValidator.ValidateDrawRepaymentPaymentRequest(createPaymentRequest, loan, ct);
        }
    }

    public async Task SendNotification(CreatePaymentRequestModel createPaymentRequest,
        PaymentRequestEntity paymentRequestEntity, IEnumerable<InvoiceModel> existingInvoices,
        CancellationToken ct)
    {
        // Draw repayment requests don't send notifications
        await Task.CompletedTask;
    }

    public async Task ProcessAdditionalLogic(CreatePaymentRequestModel createPaymentRequest, CancellationToken ct)
    {
        // No additional processing needed for draw repayments
        await Task.CompletedTask;
    }
}
