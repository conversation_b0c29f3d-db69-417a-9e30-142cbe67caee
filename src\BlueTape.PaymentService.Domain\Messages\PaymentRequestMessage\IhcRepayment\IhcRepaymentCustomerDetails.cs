using System.Text.Json.Serialization;

namespace BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.IhcRepayment;

public class IhcRepaymentCustomerDetails
{
    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;

    [JsonPropertyName("accountId")]
    public string AccountId { get; set; } = string.Empty;

    [JsonPropertyName("name")]
    public string Name { get; set; } = string.Empty;
}
