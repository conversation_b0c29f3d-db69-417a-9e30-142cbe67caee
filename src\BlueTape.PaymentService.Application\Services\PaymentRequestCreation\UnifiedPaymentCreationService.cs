using AutoMapper;
using BlueTape.InvoiceClient.Abstractions;
using BlueTape.InvoiceService.Models.Invoice;
using BlueTape.LS.DTOs.Loan;
using BlueTape.PaymentService.Application.Abstractions.Senders;
using BlueTape.PaymentService.Application.Abstractions.Services;
using BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation;
using BlueTape.PaymentService.Application.Models;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Models.PaymentRequest;
using BlueTape.PaymentService.PaymentFlowTemplatesEngine.Abstractions;
using BlueTape.PaymentService.UnitOfWork.Abstractions;
using Microsoft.Extensions.Logging;

namespace BlueTape.PaymentService.Application.Services.PaymentRequestCreation;

/// <summary>
/// Unified payment creation service that uses strategies to handle different payment types
/// </summary>
public class UnifiedPaymentCreationService : BasePaymentRequestCreationService, IUnifiedPaymentCreationService
{
    private readonly IEnumerable<IPaymentCreationStrategy> _strategies;

    public UnifiedPaymentCreationService(
        IMapper mapper,
        IUnitOfWork unitOfWork,
        IPaymentFlowTemplatesEngine templatesEngine,
        IInvoiceHttpClient invoiceHttpClient,
        ILogger<UnifiedPaymentCreationService> logger,
        IOperationSyncMessageSender operationSyncMessageSender,
        IPaymentRequestPayableService paymentRequestPayableService,
        ILoanManagementService loanManagementService,
        IEnumerable<IPaymentCreationStrategy> strategies)
        : base(mapper, unitOfWork, templatesEngine, invoiceHttpClient, logger, operationSyncMessageSender, paymentRequestPayableService, loanManagementService)
    {
        _strategies = strategies;
    }

    /// <summary>
    /// Creates a payment request using the appropriate strategy based on flow template code
    /// </summary>
    public async Task<PaymentRequestModel> CreatePaymentRequest(CreatePaymentRequestModel createPaymentRequest, string createdBy, CancellationToken ct)
    {
        var strategy = GetStrategy(createPaymentRequest.FlowTemplateCode);

        // Process any additional logic specific to this payment type
        await strategy.ProcessAdditionalLogic(createPaymentRequest, ct);

        // Use the base implementation with strategy-specific validation and notification
        return await Add(createPaymentRequest, createdBy, ct);
    }

    protected override async Task ValidatePaymentRequest(CreatePaymentRequestModel createPaymentRequest,
        List<InvoiceModel> existingInvoices,
        List<PaymentRequestPayableModel> processedPayables,
        LoanDto? loan, CancellationToken ct)
    {
        var strategy = GetStrategy(createPaymentRequest.FlowTemplateCode);
        await strategy.ValidatePaymentRequest(createPaymentRequest, existingInvoices, processedPayables, loan, ct);
    }

    protected override async Task SendNotification(CreatePaymentRequestModel createPaymentRequest,
        PaymentRequestEntity paymentRequestEntity,
        IEnumerable<InvoiceModel> existingInvoices, CancellationToken ct)
    {
        var strategy = GetStrategy(createPaymentRequest.FlowTemplateCode);
        await strategy.SendNotification(createPaymentRequest, paymentRequestEntity, existingInvoices, ct);
    }

    private IPaymentCreationStrategy GetStrategy(string flowTemplateCode)
    {
        var strategy = _strategies.FirstOrDefault(s => s.CanHandle(flowTemplateCode));
        if (strategy == null)
        {
            throw new InvalidOperationException($"No strategy found for flow template code: {flowTemplateCode}");
        }
        return strategy;
    }
}
