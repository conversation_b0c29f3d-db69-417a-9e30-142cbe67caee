using BlueTape.InvoiceService.Models.Invoice;
using BlueTape.LS.DTOs.Loan;
using BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation;
using BlueTape.PaymentService.Application.Abstractions.Validators;
using BlueTape.PaymentService.Application.Models;
using BlueTape.PaymentService.Domain.Constants;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Models.PaymentRequest;

namespace BlueTape.PaymentService.Application.Services.PaymentRequestCreation.Strategies;

/// <summary>
/// Strategy for handling Invoice Disbursement V2 requests
/// </summary>
public class InvoiceDisbursementV2Strategy : IPaymentCreationStrategy
{
    private readonly IPaymentRequestValidator _paymentRequestValidator;

    public InvoiceDisbursementV2Strategy(IPaymentRequestValidator paymentRequestValidator)
    {
        _paymentRequestValidator = paymentRequestValidator;
    }

    public bool CanHandle(string flowTemplateCode)
    {
        return flowTemplateCode == DomainConstants.InvoiceDisbursementV2;
    }

    public async Task ValidatePaymentRequest(CreatePaymentRequestModel createPaymentRequest,
        List<InvoiceModel> existingInvoices, List<PaymentRequestPayableModel> processedPayables,
        LoanDto? loan, CancellationToken ct)
    {
        // Uses the same validation as Invoice Payment V2
        await _paymentRequestValidator.ValidateInvoicePaymentRequest(
            createPaymentRequest, existingInvoices, processedPayables, loan, ct);
    }

    public async Task SendNotification(CreatePaymentRequestModel createPaymentRequest,
        PaymentRequestEntity paymentRequestEntity, IEnumerable<InvoiceModel> existingInvoices,
        CancellationToken ct)
    {
        // Invoice disbursement V2 requests don't send notifications
        await Task.CompletedTask;
    }

    public async Task ProcessAdditionalLogic(CreatePaymentRequestModel createPaymentRequest, CancellationToken ct)
    {
        // No additional processing needed for invoice disbursement V2
        await Task.CompletedTask;
    }
}
