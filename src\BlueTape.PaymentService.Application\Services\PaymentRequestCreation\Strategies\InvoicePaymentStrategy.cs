using BlueTape.InvoiceService.Models.Invoice;
using BlueTape.LS.DTOs.Loan;
using BlueTape.PaymentService.Application.Abstractions.Senders;
using BlueTape.PaymentService.Application.Abstractions.Services.External.Ledger;
using BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation;
using BlueTape.PaymentService.Application.Abstractions.Validators;
using BlueTape.PaymentService.Application.Models;
using BlueTape.PaymentService.Domain.Constants;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Messages;
using BlueTape.PaymentService.Domain.Models.PaymentRequest;
using BlueTape.ServiceBusMessaging.Attributes;
using TinyHelpers.Extensions;

namespace BlueTape.PaymentService.Application.Services.PaymentRequestCreation.Strategies;

/// <summary>
/// Strategy for handling Invoice Payment requests (both V1 and V2)
/// </summary>
public class InvoicePaymentStrategy : IPaymentCreationStrategy
{
    private readonly IPaymentRequestValidator _paymentRequestValidator;
    private readonly ILedgerService _ledgerService;
    private readonly INotificationMessageSender _notificationMessageSender;

    public InvoicePaymentStrategy(
        IPaymentRequestValidator paymentRequestValidator,
        ILedgerService ledgerService,
        INotificationMessageSender notificationMessageSender)
    {
        _paymentRequestValidator = paymentRequestValidator;
        _ledgerService = ledgerService;
        _notificationMessageSender = notificationMessageSender;
    }

    public bool CanHandle(string flowTemplateCode)
    {
        return flowTemplateCode is
            DomainConstants.InvoicePayment or
            DomainConstants.InvoicePaymentCard or
            DomainConstants.InvoicePaymentV2;
    }

    public async Task ValidatePaymentRequest(CreatePaymentRequestModel createPaymentRequest,
        List<InvoiceModel> existingInvoices, List<PaymentRequestPayableModel> processedPayables,
        LoanDto? loan, CancellationToken ct)
    {
        await _paymentRequestValidator.ValidateInvoicePaymentRequest(
            createPaymentRequest, existingInvoices, processedPayables, loan, ct);
    }

    public async Task SendNotification(CreatePaymentRequestModel createPaymentRequest,
        PaymentRequestEntity paymentRequestEntity, IEnumerable<InvoiceModel> existingInvoices,
        CancellationToken ct)
    {
        await _ledgerService.HandlePaymentRequestCreated(paymentRequestEntity.Id, createPaymentRequest, ct);

        var messagesToSend = new ServiceBusMessageBt<NotificationMessagePayloadV2>(new NotificationMessagePayloadV2
        {
            Id = paymentRequestEntity.Id,
            NotificationType = NotificationType.PaymentRequestCreated
        });

        await _notificationMessageSender.SendMessage(messagesToSend, ct);
    }

    public async Task ProcessAdditionalLogic(CreatePaymentRequestModel createPaymentRequest, CancellationToken ct)
    {
        // Process discount details for invoice payments
        var discountDetails = createPaymentRequest.PaymentRequestDiscounts;
        if (!discountDetails.IsEmpty())
        {
            var createDiscountDetails = discountDetails.Select(x => new CreatePaymentRequestFeeModel()
            {
                Amount = x.Amount,
                CompanyId = x.CompanyId!,
                Type = x.Type
            });
            createPaymentRequest.PaymentRequestFees.AddRange(createDiscountDetails);
        }

        // Additional processing logic can be added here if needed
        await Task.CompletedTask;
    }
}
