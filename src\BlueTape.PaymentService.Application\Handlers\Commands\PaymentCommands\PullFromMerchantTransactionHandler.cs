﻿using BlueTape.Integrations.Aion.Ach.CreateAchTransfer;
using BlueTape.Integrations.Aion.AzureTableStorage.Abstractions;
using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BlueTape.PaymentService.Application.Abstractions.Mappers;
using BlueTape.PaymentService.Application.Abstractions.Services;
using BlueTape.PaymentService.Application.Abstractions.Services.External;
using BlueTape.PaymentService.Application.Abstractions.Services.Notification;
using BlueTape.PaymentService.Application.Constants;
using BlueTape.PaymentService.Application.Mappers;
using BlueTape.PaymentService.Application.Models.Commands;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Extensions;
using BlueTape.PaymentService.PaymentFlowTemplatesEngine.Models.Raw;
using BlueTape.PaymentService.UnitOfWork.Abstractions;
using BlueTape.Utilities.Providers;
using MediatR;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace BlueTape.PaymentService.Application.Handlers.Commands.PaymentCommands;

public class PullFromMerchantTransactionHandler(
    IAionServiceV2 aionService,
    IDateProvider dateProvider,
    ILogger<PullFromMerchantTransactionHandler> logger,
    ITransactionNumberService transactionNumberService,
    IUnitOfWork unitOfWork,
    IPaymentRequestService paymentRequestService,
    IMediator mediator,
    ISlackNotificationService slackNotificationService,
    IStatusService statusService,
    IAzureStorageTransactionRepository azureStorageTransactionRepository,
    IOptions<RawFlowTemplateOptions> options,
    IPaymentConfigService paymentConfigService)
    : AionBaseHandler(logger,
        transactionNumberService,
        unitOfWork,
        dateProvider,
        paymentRequestService,
        aionService,
        mediator,
        slackNotificationService,
        statusService,
        azureStorageTransactionRepository,
        options,
        paymentConfigService), IRequestHandler<PullFromMerchantCommand>
{
    private readonly IAionServiceV2 _aionService = aionService;

    public Task Handle(PullFromMerchantCommand request, CancellationToken cancellationToken)
    {
        return base.Handle(request, cancellationToken);
    }

    protected override async Task<PaymentRequestCommandEntity> ProcessTransactionTransfer(PaymentRequestCommandEntity command, CancellationToken ct)
    {
        var paymentRequest = command.PaymentRequest ?? throw new ArgumentNullException(nameof(command.PaymentRequest), "Payment request cannot be null.");
        var transaction = command.Transaction ?? throw new ArgumentNullException(nameof(command.Transaction), "Transaction cannot be null.");

        var companyId = paymentRequest.PayeeId ?? throw new ArgumentNullException(nameof(paymentRequest.PayeeId));
        var bankAccountId = transaction.ReceiverAccountId ?? throw new ArgumentNullException(nameof(transaction.ReceiverAccountId));

        logger.LogInformation("Started a pull from merchant transaction. Transaction number for the request: {number}, paymentRequestId: {paymentRequestId}", transaction.TransactionNumber, command.PaymentRequestId);

        var achPullModel = new CreateAchModel
        {
            Amount = transaction.Amount,
            Receiver = new ReceiverModel
            {
                CompanyId = companyId,
                BankAccountId = bankAccountId,
            },

            TransactionNumber = transaction.TransactionNumber!,
            TransactionId = transaction.Id.ToString(),
            Description = transaction.PublicTransactionNumber ?? ApplicationConstants.BlueTape,
            Addenda = [ApplicationConstants.BlueTape],
            OriginatorAccountCode = transaction.OriginatorAccountId.MapAccountCodeToEnum(),
        };
        var result = await _aionService.CreateExternalTransfer(
            achPullModel,
            command.PaymentRequestId,
            paymentRequest.PaymentSubscription,
            TransactionType.Pull,
            transaction.PaymentMethod.MapPaymentMethodToAionType(),
            ct);

        logger.LogInformation("Finished a pull from merchant transaction, paymentRequestId: {paymentRequestId}", command.PaymentRequestId);

        command.Transaction.MetaData = result?.AionResponse ?? string.Empty;

        if (!string.IsNullOrEmpty(result.AionReferenceId))
            command.Transaction.ReferenceNumber = result.AionReferenceId;

        command.Transaction.ProcessingFees = result.AionFee;

        return command;
    }

    protected override string GetHandlerName()
    {
        return $"{ApplicationConstants.ScheduledEventProcessor}:{nameof(PullFromMerchantTransactionHandler)}";
    }
}
