using AutoMapper;
using BlueTape.CompanyService.Companies;
using BlueTape.PaymentService.API.Queries;
using BlueTape.PaymentService.API.ViewModels;
using BlueTape.PaymentService.API.ViewModels.Base;
using BlueTape.PaymentService.API.ViewModels.Company;
using BlueTape.PaymentService.Application.Models;
using BlueTape.PaymentService.Application.Models.Base;
using BlueTape.PaymentService.Application.Models.Company;
using BlueTape.PaymentService.Domain.Entities.Filters;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Models.PaymentRequest;

namespace BlueTape.PaymentService.API.Mappers;

public class ApiProfile : Profile
{
    public ApiProfile()
    {
        CreateMap<PaymentTransactionModel, PaymentTransactionViewModel>().ForMember(x => x.Reason,
            opt => opt.MapFrom(y => y.LastResult));
        CreateMap<PaymentRequestModel, PaymentRequestViewModel>();
        CreateMap<EventLogModel, PaymentRequestNotificationViewModel>()
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status.ToString()))
            .ReverseMap()
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => Enum.Parse<EventStatus>(src.Status)));

        CreateMap<PaymentRequestDetailsModel, PaymentRequestDetailsViewModel>();
        CreateMap<PaymentRequestFeeModel, PaymentRequestFeeViewModel>();
        CreateMap<PaymentRequestPayableModel, PaymentRequestPayableViewModel>();
        CreateMap<PaymentRequestFilterQuery, PaymentRequestFilter>()
            .ForMember(x => x.DateFrom,
                opt => opt.MapFrom(y => y.From))
            .ForMember(x => x.PageSize,
                opt => opt.MapFrom(y => y.Items))
            .ForMember(x => x.PageNumber,
                opt => opt.MapFrom(y => y.Page))
            .ForMember(x => x.PayerId,
                opt => opt.MapFrom(y => y.CustomerId))
            .ForMember(x => x.DateTo,
            opt => opt.MapFrom(y => y.To));
        CreateMap<TransactionFilterQuery, TransactionFilter>()
            .ForMember(x => x.DateFrom, opt => opt.MapFrom(y => y.From))
            .ForMember(x => x.PageSize, opt => opt.MapFrom(y => y.Items))
            .ForMember(x => x.PageNumber, opt => opt.MapFrom(y => y.Page))
            .ForMember(x => x.DateTo, opt => opt.MapFrom(y => y.To));
        CreateMap<PaginatedResult<PaymentRequestModel>, PaginatedResultViewModel<PaymentRequestViewModel>>();
        CreateMap<PaginatedResult<PaymentTransactionModel>, PaginatedResultViewModel<PaymentTransactionViewModel>>();

        CreateMap<CreatePaymentRequestViewModel, CreatePaymentRequestModel>();
        CreateMap<CreatePaymentRequestFeeViewModel, CreatePaymentRequestFeeModel>();
        CreateMap<CreatePaymentRequestPayableViewModel, CreatePaymentRequestPayableModel>();
        CreateMap<CreatePaymentRequestTransactionViewModel, CreatePaymentRequestTransactionModel>();
        CreateMap<PaymentRequestCommandModel, PaymentRequestCommandViewModel>()
                        .ForMember(x => x.SequenceNumber,
                opt => opt.MapFrom(y => y.Transaction!.SequenceNumber));

        CreateMap<PaymentTransactionHistoryModel, PaymentTransactionHistoryViewModel>();

        CreateMap<CompanyModel, CompanyViewModel>();
        CreateMap<ForbidCompanyRequestViewModel, ForbidCompanyRequest>();        CreateMap<PausePaymentRequestViewModel, PausePaymentRequestModel>();
        CreateMap<MarkPaymentRequestSucceededViewModel, MarkPaymentRequestSucceededModel>();
        CreateMap<PaymentApprovalRequestViewModel, PaymentApprovalRequest>()
            .ForMember(dest => dest.PaymentMethod, opt => opt.MapFrom(src => !string.IsNullOrEmpty(src.PaymentMethod) ? 
                Enum.Parse<PaymentMethod>(src.PaymentMethod, true) : (PaymentMethod?)null));
    }
}