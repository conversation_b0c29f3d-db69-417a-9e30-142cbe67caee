﻿using BlueTape.PaymentService.API.ViewModels;
using BlueTape.PaymentService.Domain.Enums;

namespace BlueTape.PaymentService.Application.Tests.Models;
public class CreateTransactionRequests
{
    public static readonly CreatePaymentRequestTransactionViewModel ValidTransactionRequest = new()
    {
        TransactionType = PaymentTransactionType.AchPush,
        PaymentMethod = PaymentMethod.Card,
        OriginatorAccountId = "originator123",
        ReceiverAccountId = "receiver456",
        Amount = 100.00m,
        Discount = 0.00m,
        Currency = "USD",
        Date = DateOnly.FromDateTime(DateTime.Now),
        TransactionNumber = "*********",
        ReferenceNumber = "REF123",
        MetaData = "Some metadata",
        Reason = "Payment for services",
    };
}
