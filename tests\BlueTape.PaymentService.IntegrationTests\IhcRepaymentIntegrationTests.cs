using BlueTape.PaymentService.Application.Services.PaymentRequestCreation;
using BlueTape.PaymentService.Domain.Constants;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage.IhcRepayment;
using BlueTape.PaymentService.Domain.Models.PaymentRequest;
using Microsoft.Extensions.DependencyInjection;
using System.Text.Json;
using Xunit;

namespace BlueTape.PaymentService.IntegrationTests;

/// <summary>
/// Integration tests for IHC Repayment functionality
/// </summary>
public class IhcRepaymentIntegrationTests : BaseIntegrationTest
{
    [Fact]
    public async Task IhcRepaymentStrategy_WithAchPayment_ShouldCreatePaymentRequest()
    {
        // Arrange
        var unifiedService = ServiceProvider.GetRequiredService<UnifiedPaymentCreationService>();
        var createPaymentRequest = CreateIhcRepaymentRequest("ach");

        // Act
        var result = await unifiedService.CreatePaymentRequest(createPaymentRequest, "BlueTape.LMS.ProcessDue", CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.NotEqual(Guid.Empty, result.Id);
        Assert.Equal(DomainConstants.IhcRepayment, result.FlowTemplateCode);
        Assert.Equal(5000m, result.RequestedAmount);
        Assert.Equal("USD", result.Currency);
    }

    [Fact]
    public async Task IhcRepaymentStrategy_WithCardPayment_ShouldCreatePaymentRequest()
    {
        // Arrange
        var unifiedService = ServiceProvider.GetRequiredService<UnifiedPaymentCreationService>();
        var createPaymentRequest = CreateIhcRepaymentRequest("card");

        // Act
        var result = await unifiedService.CreatePaymentRequest(createPaymentRequest, "BlueTape.LMS.ProcessDue", CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.NotEqual(Guid.Empty, result.Id);
        Assert.Equal(DomainConstants.IhcRepayment, result.FlowTemplateCode);
        Assert.Equal(5000m, result.RequestedAmount);
        Assert.Equal("USD", result.Currency);
    }

    [Fact]
    public void IhcRepaymentMessage_ShouldSerializeCorrectly()
    {
        // Arrange
        var message = CreateIhcRepaymentMessage("ach");

        // Act
        var json = JsonSerializer.Serialize(message, new JsonSerializerOptions(JsonSerializerDefaults.Web));
        var deserializedMessage = JsonSerializer.Deserialize<IhcRepaymentRequestMessage>(json, new JsonSerializerOptions(JsonSerializerDefaults.Web));

        // Assert
        Assert.NotNull(deserializedMessage);
        Assert.Equal(DomainConstants.IhcRepayment, deserializedMessage.FlowTemplateCode);
        Assert.Equal("BlueTape.LMS.ProcessDue", deserializedMessage.CreatedBy);
        Assert.Equal(5000m, deserializedMessage.PaymentRequestDetails.RequestedAmount);
        Assert.Equal("ach", deserializedMessage.PaymentRequestDetails.PaymentMethod);
    }

    [Fact]
    public void IhcRepaymentMessage_WithCardPayment_ShouldSerializeCorrectly()
    {
        // Arrange
        var message = CreateIhcRepaymentMessage("card");

        // Act
        var json = JsonSerializer.Serialize(message, new JsonSerializerOptions(JsonSerializerDefaults.Web));
        var deserializedMessage = JsonSerializer.Deserialize<IhcRepaymentRequestMessage>(json, new JsonSerializerOptions(JsonSerializerDefaults.Web));

        // Assert
        Assert.NotNull(deserializedMessage);
        Assert.Equal(DomainConstants.IhcRepayment, deserializedMessage.FlowTemplateCode);
        Assert.Equal("card", deserializedMessage.PaymentRequestDetails.PaymentMethod);
    }

    private CreatePaymentRequestModel CreateIhcRepaymentRequest(string paymentMethod)
    {
        return new CreatePaymentRequestModel
        {
            FlowTemplateCode = DomainConstants.IhcRepayment,
            Amount = 5000m,
            Currency = "USD",
            PaymentMethod = Enum.Parse<BlueTape.PaymentService.Domain.Enums.PaymentMethod>(paymentMethod, true),
            Date = DateOnly.FromDateTime(DateTime.Parse("2025-03-21")),
            PayerId = "67bc002907fe47ec12",
            CustomerAccountId = "67a1541b87e86fcb70",
            DrawId = Guid.Parse("b62f3d66-21a5-4625-bba6-f8cec368768f"),
            SubjectType = BlueTape.LS.Domain.Enums.SubjectType.Draw,
            RequestType = BlueTape.PaymentService.Domain.Enums.PaymentRequestType.IhcRepayment,
            PaymentRequestPayables = new List<CreatePaymentRequestPayableModel>(),
            PaymentRequestFees = new List<CreatePaymentRequestFeeModel>()
        };
    }

    private IhcRepaymentRequestMessage CreateIhcRepaymentMessage(string paymentMethod)
    {
        return new IhcRepaymentRequestMessage
        {
            FlowTemplateCode = DomainConstants.IhcRepayment,
            BlueTapeCorrelationId = "1cd69455-7d7c-4292-a603-eb0ad921b16d",
            CreatedBy = "BlueTape.LMS.ProcessDue",
            PaymentRequestDetails = new IhcRepaymentRequestDetails
            {
                Date = DateTime.Parse("2025-03-21"),
                Currency = "USD",
                RequestedAmount = 5000.00m,
                PaymentMethod = paymentMethod,
                DrawDetails = new IhcRepaymentDrawDetails
                {
                    Id = Guid.Parse("b62f3d66-21a5-4625-bba6-f8cec368768f"),
                    Amount = 5000.00m
                },
                CustomerDetails = new IhcRepaymentCustomerDetails
                {
                    Id = "67bc002907fe47ec12",
                    AccountId = "67a1541b87e86fcb70",
                    Name = "John Smith"
                }
            }
        };
    }
}
