﻿using BlueTape.PaymentService.CompatibilityService.Abstractions.Service.DrawRepaymentManual;
using BlueTape.PaymentService.CompatibilityService.Extensions;
using BlueTape.PaymentService.CompatibilityService.Models;
using BlueTape.PaymentService.CompatibilityService.Services.Base;
using BlueTape.PaymentService.DataAccess.Mongo.Abstractions.Repositories;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.Operation;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.Transaction;
using BlueTape.PaymentService.Domain.Constants;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Enums.Legacy;
using BlueTape.PaymentService.Domain.Extensions;
using BlueTape.PaymentService.Domain.Messages.PaymentRequestMessage;
using MongoDB.Bson;
using System.Globalization;
using TinyHelpers.Extensions;

namespace BlueTape.PaymentService.CompatibilityService.Services.DrawRepaymentManual;

public class DrawRepaymentManualCompatibilityMapper(IBankAccountRepository bankAccountRepository) : BaseCompatibilityMapper(bankAccountRepository), IDrawRepaymentManualCompatibilityMapper
{
    public override OperationEntity MapFromPaymentRequestToOperation(PaymentRequestEntity paymentRequest, string ownerId)
    {
        var operationStatus = paymentRequest.GetDrawRepaymentOperationStatus().ToString();
        var firstTransaction = paymentRequest.Transactions.Where(x => x.TransactionType == PaymentTransactionType.AchPull).MinBy(x => x.CreatedAt);

        return new OperationEntity()
        {
            BlueTapeId = ObjectId.GenerateNewId().ToString()!,
            OwnerId = ownerId,
            Status = operationStatus,
            Type = paymentRequest.RequestType.MapFromPaymentRequestTypeToOperationType(),
            Amount = paymentRequest.Amount,
            Date = paymentRequest.Date.ToDateTime(new TimeOnly(), DateTimeKind.Utc),
            Metadata = new OperationMetadataEntity
            {
                PayerId = paymentRequest.PayerId ?? string.Empty,
                PaymentMethod = paymentRequest.PaymentMethod.ToString().ToLower(),
                PaymentDate = paymentRequest.Date.ToString(),
                LmsPaymentId = paymentRequest.PaymentRequestDetails?.LMSPaymentId.ToString(),
            },
            PaymentRequestId = paymentRequest.Id.ToString(),
            CreatedBy = DomainConstants.PaymentService,
            FirstTransactionDate = firstTransaction?.CreatedAt,
            PaymentProvider = PaymentProvider.Aion.ToString(),
        };
    }

    public async Task<List<TransactionEntity>> MapFromPaymentTransactionsToLegacyTransactions(PaymentRequestEntity paymentRequest, OperationEntity operation, CancellationToken ctx)
    {
        var transactions = paymentRequest.Transactions;
        var operationId = operation.BlueTapeId;
        var payerId = paymentRequest.PayerId;
        var legacyTransactions = new List<TransactionEntity>();
        var paymentRequestMetadata =
            paymentRequest.PaymentRequestDetails?.GetMetadata<ManualPaymentDetails>(MetadataConstants.ManualPaymentDetails);

        await transactions.ForEachAsync(async transaction =>
        {
            var metadata = await GetTransactionMetadata(transaction, operation.Type, transaction.Amount, paymentRequest.PaymentRequestDetails?.LMSPaymentId.ToString(), ctx);

            var legacyTransaction = new TransactionEntity()
            {
                OperationId = operationId,
                Type = LegacyPaymentFlowConstants.InternalTransferTransactionType,
                PayerId = payerId,
                Amount = transaction.Amount,
                Currency = transaction.Currency,
                Fee = 0,
                PaymentMethod = PaymentMethod.Ach.ToString().ToLower(),
                Status = OperationStatus.PLACED.ToString(),
                CreatedBy = paymentRequestMetadata?.UserName is not null ? $"Created by ops team {paymentRequestMetadata?.UserName}" : null,
                Provider = PaymentProvider.Aion.ToString(),
                PaymentTransactionId = transaction.Id.ToString(),
                PaymentRequestId = operation.Type == LegacyPaymentFlowConstants.InvoicePaymentOperationType ? paymentRequest.Id.ToString() : null,
                Date = transaction.Date.ToDateTime(new TimeOnly()),

                Metadata = metadata,
                ManualPaymentData = new()
                {
                    ExternalReferenceNumber = paymentRequestMetadata?.ExternalReferenceNumber,
                    ExternalTransactionAmount = paymentRequestMetadata?.ExternalTransactionAmount,
                    ManualPaymentMethod = paymentRequestMetadata?.ManualPaymentMethod,
                    UserId = paymentRequestMetadata?.UserId,
                }
            };

            legacyTransactions.Add(legacyTransaction);
        }, cancellationToken: ctx);

        return legacyTransactions;
    }

    public override Task<UpdateTransactionEntity> MapSyncModelToUpdateTransactionEntity(SyncTransactionModel syncTransactionModel, CancellationToken ctx)
    {
        var updateTransactionEntity = new UpdateTransactionEntity
        {
            Date = syncTransactionModel.Date,
            Status = syncTransactionModel.Status.MapFromPaymentTransactionStatusToLegacyTransactionStatus().ToString(),
            StatusDataAccountId = syncTransactionModel.ReceiverAccountId,
            StatusDataTransactionNumber = syncTransactionModel.ReferenceNumber,
            OriginalReference = syncTransactionModel.TransactionNumber,
            MetadataTransactionNumber = syncTransactionModel.ReferenceNumber,
            ApiDateTime = syncTransactionModel.ExecutedAt,
            UpdatedBy = DomainConstants.PaymentService,
            StatusCode = syncTransactionModel.StatusCode,
            PaymentTransactionId = syncTransactionModel.PaymentTransactionId,
            Provider = syncTransactionModel.Provider,
            PublicTransactionNumber = syncTransactionModel.PublicTransactionNumber,
            StatusReason = syncTransactionModel.StatusReason,
            StatusDataTransactionStatus = syncTransactionModel.StatusDataTransactionStatus,
        };

        return Task.FromResult(updateTransactionEntity);
    }

    protected Task<TransactionMetadataEntity> GetTransactionMetadata(PaymentTransactionEntity? transaction, string operationType, decimal legacyTransactionAmount, string lmsPaymentId, CancellationToken ctx)
    {
        var entity = new TransactionMetadataEntity()
        {
            TransactionType = LegacyTransactionType.INTERNAL.ToString(),
            AccountNumber = transaction?.OriginatorAccountId ?? string.Empty,
            Payload = string.Empty,
            LmsPaymentId = operationType == LegacyPaymentFlowConstants.InvoicePaymentOperationType ? lmsPaymentId : null,
            StatusData = new TransactionStatusDataEntity()
            {
                TransactionAmountCents = (double)legacyTransactionAmount * 100,
                Account = new TransactionAccountEntity()
                {
                    AccountId = transaction?.ReceiverAccountId,
                    BalanceCents = 0,
                    HoldBalanceCents = 0,
                    Status = LegacyPaymentFlowConstants.DrawRepaymentTransactionAccountStatus
                },
                TransactionNumber = transaction?.ReferenceNumber,
                TransactionStatus = transaction?.Status.MapFromLegacyTransactionStatusToStatusDataTransactionStatus().ToString(),
                ApiMetadata = new TransactionApiMetadataEntity()
                {
                    DateTime = transaction?.ExecutedAt.ToString(CultureInfo.InvariantCulture),
                    OriginalReference = transaction?.TransactionNumber,
                    Reference = transaction?.TransactionNumber,
                    Type = LegacyPaymentFlowConstants.TransactionApiMetadataPullType
                },
                StatusCode = LegacyPaymentFlowConstants.TransactionMetadataStatusCode,
                OriginalRequestBase64 = string.Empty,
            },
            PublicTransactionNumber = transaction?.PublicTransactionNumber,
        };

        return Task.FromResult(entity);
    }
}
