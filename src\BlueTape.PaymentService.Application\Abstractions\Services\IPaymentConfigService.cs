using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Models;

namespace BlueTape.PaymentService.Application.Abstractions.Services;

public interface IPaymentConfigService
{
    // Subscription-specific daily limit methods
    Task<AionDailyLimitConfig> GetAionDailyLimitConfig(PaymentSubscriptionType subscriptionType, CancellationToken ct);
    Task SetAionDailyLimitExceeded(PaymentTransactionType transactionType, PaymentSubscriptionType subscriptionType, CancellationToken ct);
    Task ResetAionDailyLimits(PaymentSubscriptionType subscriptionType, CancellationToken ct);
    Task<bool> IsAionDailyLimitExceeded(PaymentTransactionType transactionType, PaymentSubscriptionType subscriptionType, CancellationToken ct);

    // Reset all subscription limits
    Task ResetAllAionDailyLimits(CancellationToken ct);

    Task<Dictionary<PaymentSubscriptionType, AionDailyLimitConfig>> GetAllAionDailyLimitConfigs(CancellationToken ct);
}
