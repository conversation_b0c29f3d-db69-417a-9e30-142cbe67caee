using BlueTape.PaymentService.DataAccess.Abstractions.Repositories.Base;
using BlueTape.PaymentService.Domain.Entities;

namespace BlueTape.PaymentService.DataAccess.Abstractions.Repositories;

public interface IPaymentConfigRepository : IGenericRepository<PaymentConfigEntity>
{
    Task<PaymentConfigEntity?> GetByConfigKey(string configKey, CancellationToken ct);
    Task<List<PaymentConfigEntity>> GetByConfigKeys(IEnumerable<string> configKeys, CancellationToken ct);
}
