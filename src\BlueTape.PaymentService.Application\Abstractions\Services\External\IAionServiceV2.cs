using BlueTape.Integrations.Aion;
using BlueTape.Integrations.Aion.Accounts;
using BlueTape.Integrations.Aion.Ach.CreateAchTransfer;
using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BlueTape.Integrations.Aion.Internal;
using BlueTape.Integrations.Aion.Transactions;
using BlueTape.Utilities.Models;

namespace BlueTape.PaymentService.Application.Abstractions.Services.External;

public interface IAionServiceV2
{
    Task<BlueTapeTransactionResponseModel?> CreateExternalTransfer(CreateAchModel createInternalModel, Guid paymentRequestId, PaymentSubscriptionType paymentSubscription, TransactionType transactionType, AionPaymentMethodType paymentMethod, CancellationToken ct);

    Task<BlueTapeTransactionResponseModel?> CreateInternalTransfer(CreateInternalModel requestModel, Guid paymentRequestId, PaymentSubscriptionType paymentSubscription, CancellationToken ct);

    Task<decimal> GetAccountBalance(AccountCodeType accountCodeType, Guid paymentRequestId, PaymentSubscriptionType paymentSubscription, CancellationToken ct);

    Task<PaginatedResponse<TransactionListObj>?> GetAionAccountTransactions(TransactionsQuery query, CancellationToken ct);

    Task<List<AccountResponseObj?>?> GetAccounts(CancellationToken cancellationToken);
}
