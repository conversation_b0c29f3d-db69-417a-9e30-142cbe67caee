﻿using BlueTape.PaymentService.Domain.Enums;

namespace BlueTape.PaymentService.Application.Helpers;

public static class DisplayHelper
{
    private const string Ach = "ACH";
    private const string Credit = "Trade Credit";
    private const string ArAdvance = "AR Advance";

    public static string GetPaymentType(PaymentRequestType paymentRequestType)
    {
        return paymentRequestType switch
        {
            PaymentRequestType.InvoicePayment => Ach,
            PaymentRequestType.InvoicePaymentV2 or
                PaymentRequestType.InvoiceDisbursementV2 or
                PaymentRequestType.FactoringDisbursement or
                PaymentRequestType.FactoringFinalPayment => ArAdvance,
            PaymentRequestType.FinalPayment or
                PaymentRequestType.FinalPaymentV2 or
                PaymentRequestType.DrawDisbursement or
                PaymentRequestType.DrawRepayment => Credit,
            _ => Ach
        };
    }
}
