﻿using BlueTape.PaymentService.Domain.Enums;

namespace BlueTape.PaymentService.Application.Models;

public class CreatePaymentRequestTransactionModel
{
    public PaymentTransactionType TransactionType { get; set; }
    public PaymentMethod PaymentMethod { get; set; }
    public string? OriginatorAccountId { get; set; }
    public string? ReceiverAccountId { get; set; }
    public decimal Amount { get; set; }
    public decimal Discount { get; set; }
    public string Currency { get; set; } = string.Empty;
    public DateOnly Date { get; set; }
    public string TransactionNumber { get; set; } = string.Empty;
    public string ReferenceNumber { get; set; } = string.Empty;
    public string MetaData { get; set; } = string.Empty;
    public string Reason { get; set; } = string.Empty;
}
