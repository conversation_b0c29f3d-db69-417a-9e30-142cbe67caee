using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BlueTape.PaymentService.Application.Abstractions.Services;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Models;
using Microsoft.AspNetCore.Mvc;

namespace BlueTape.PaymentService.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
[Tags("Aion Daily Limits Testing")]
public class AionDailyLimitsController(IPaymentConfigService paymentConfigService) : ControllerBase
{
    [HttpGet("{subscriptionType}")]
    public async Task<ActionResult<AionDailyLimitConfig>> GetDailyLimits(PaymentSubscriptionType subscriptionType, CancellationToken ct)
    {
        var config = await paymentConfigService.GetAionDailyLimitConfig(subscriptionType, ct);
        return Ok(config);
    }

    [HttpGet("{subscriptionType}/check/{transactionType}")]
    public async Task<ActionResult<bool>> CheckDailyLimit(PaymentSubscriptionType subscriptionType, PaymentTransactionType transactionType, CancellationToken ct)
    {
        var isExceeded = await paymentConfigService.IsAionDailyLimitExceeded(transactionType, subscriptionType, ct);
        return Ok(new
        {
            SubscriptionType = subscriptionType.ToString(),
            TransactionType = transactionType.ToString(),
            IsLimitExceeded = isExceeded
        });
    }

    [HttpPost("{subscriptionType}/set/{transactionType}")]
    public async Task<ActionResult> SetDailyLimitExceeded(PaymentSubscriptionType subscriptionType, PaymentTransactionType transactionType, CancellationToken ct)
    {
        await paymentConfigService.SetAionDailyLimitExceeded(transactionType, subscriptionType, ct);
        return Ok(new
        {
            Message = $"Daily limit set to exceeded for {transactionType} on {subscriptionType}",
            SubscriptionType = subscriptionType.ToString(),
            TransactionType = transactionType.ToString(),
            Status = "Exceeded"
        });
    }

    [HttpPost("{subscriptionType}/reset")]
    public async Task<ActionResult> ResetDailyLimits(PaymentSubscriptionType subscriptionType, CancellationToken ct)
    {
        await paymentConfigService.ResetAionDailyLimits(subscriptionType, ct);
        return Ok(new
        {
            Message = $"Daily limits reset for {subscriptionType}",
            SubscriptionType = subscriptionType.ToString(),
            Status = "Reset"
        });
    }


    [HttpGet("get-all")]
    public async Task<ActionResult> GetAllDailyLimits(CancellationToken ct)
    {
        var config = await paymentConfigService.GetAllAionDailyLimitConfigs(ct);
        return Ok(config);
    }

    [HttpPost("reset-all")]
    public async Task<ActionResult> ResetAllDailyLimits(CancellationToken ct)
    {
        await paymentConfigService.ResetAllAionDailyLimits(ct);
        return Ok(new
        {
            Message = "Daily limits reset for all subscriptions",
            Subscriptions = new[] { "SUBSCRIPTION1", "SUBSCRIPTION2", "SUBSCRIPTION3" },
            Status = "Reset"
        });
    }
}
