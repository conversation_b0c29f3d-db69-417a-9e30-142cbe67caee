﻿using AutoMapper;
using BlueTape.PaymentService.CompatibilityService.Abstractions.Service.DrawRepaymentCard;
using BlueTape.PaymentService.CompatibilityService.Extensions;
using BlueTape.PaymentService.CompatibilityService.Models;
using BlueTape.PaymentService.DataAccess.Mongo.Abstractions.Repositories;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.Operation;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.Transaction;
using BlueTape.PaymentService.Domain.Constants;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Enums;

namespace BlueTape.PaymentService.CompatibilityService.Services.DrawRepaymentCard;

public class DrawRepaymentCardOperationSyncService(
    IOperationsRepository operationsRepository,
    ITransactionsRepository transactionsRepository,
    IDrawRepaymentCardTransactionSyncService transactionSyncService,
    ILoanApplicationRepository loanApplicationRepository,
    IMapper mapper) : IDrawRepaymentCardOperationSyncService
{
    public async Task PerformOperation(PaymentRequestEntity paymentRequest, CancellationToken ct)
    {
        var loanApplication =
            await loanApplicationRepository.GetByLmsId(paymentRequest.PaymentRequestDetails?.DrawId?.ToString(), ct);
        // for card repayment, only one operation/transaction would be possible
        var transaction = (await transactionsRepository.GetByTransactionNumber(paymentRequest?.PaymentRequestDetails?.ParentReferenceNumb﻿er, ct)).FirstOrDefault();
        var operation = (await operationsRepository.GetByIds([transaction.OperationId], ct))
            .FirstOrDefault(x => x.Type == LegacyPaymentFlowConstants.DrawRepaymentOperationType && x.OwnerId == loanApplication.BlueTapeId);

        await operationsRepository.UpdateById(operation.BlueTapeId, new UpdateOperationEntity()
        {
            PaymentRequestId = paymentRequest.Id.ToString(),
            PullResult = DateTime.Now,
            PaymentProvider = PaymentProvider.Aion,
        }, ct);

        await transactionsRepository.Update(transaction.Id, new UpdateTransactionEntity { IsAchInternalCreated = true }, ct);

        await transactionSyncService.PerformTransactions(paymentRequest, operation, ct);
    }

    public async Task SyncOperation(PaymentRequestEntity paymentRequest, CancellationToken cancellationToken)
    {
        var existingOperation =
           (await operationsRepository.GetByPaymentRequestId(paymentRequest.Id.ToString(), cancellationToken)).LastOrDefault();

        string? ownerId = null;
        if (string.IsNullOrEmpty(existingOperation?.OwnerId))
        {
            var loanApplication =
                await loanApplicationRepository.GetByLmsId(paymentRequest.PaymentRequestDetails?.DrawId?.ToString(),
                    cancellationToken);

            ownerId = loanApplication?.BlueTapeId;
        }

        var updateOperationEntity = GetUpdateOperationEntity(paymentRequest, ownerId);
        await operationsRepository.Update(paymentRequest.Id.ToString(), updateOperationEntity, cancellationToken);

        await transactionSyncService.SyncTransactions(paymentRequest, cancellationToken);
    }

    private UpdateOperationEntity GetUpdateOperationEntity(PaymentRequestEntity paymentRequest, string? ownerId)
    {
        var syncOperation = new SyncOperationModel()
        {
            Status = paymentRequest.GetDrawRepaymentOperationStatus(),
            PaymentRequestId = paymentRequest.Id.ToString(),
            OwnerId = ownerId
        };
        var updateOperationEntity = mapper.Map<UpdateOperationEntity>(syncOperation);

        return updateOperationEntity;
    }
}
