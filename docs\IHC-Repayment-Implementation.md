# IHC Repayment Implementation

## Overview
This document describes the implementation of the new `CREATE.IHC.REPAYMENT` template that replaces `ManualPaymentPull` and enables the LMS to initiate payment requests to the Payment Domain for both ACH and Card payments.

## Key Features

### 1. Unified Template for Multiple Payment Methods
- **Template Code**: `CREATE.IHC.REPAYMENT`
- **Supported Payment Methods**: ACH and Card
- **Initiated By**: BlueTape LMS (BlueTape.LMS.ProcessDue)

### 2. Integration with Unified Payment Architecture
- Uses the new Strategy Pattern architecture
- Leverages `IhcRepaymentStrategy` for processing
- Integrates seamlessly with existing payment flow

## Implementation Details

### Domain Constants
```csharp
// Added to DomainConstants.cs
public const string IhcRepayment = "CREATE.IHC.REPAYMENT";
```

### Payment Request Type
```csharp
// Added to PaymentRequestType enum
IhcRepayment = 14,
```

### Message Structure

#### ACH Payment Request
```json
{
    "flowTemplateCode": "CREATE.IHC.REPAYMENT",
    "blueTapeCorrelationId": "1cd69455-7d7c-4292-a603-eb0ad921b16d",
    "createdBy": "BlueTape.LMS.ProcessDue",
    "details": {
        "date": "2025-03-21",
        "currency": "USD",
        "requestedAmount": 5000.00,
        "paymentMethod": "ach",
        "drawDetails": {
            "id": "b62f3d66-21a5-4625-bba6-f8cec368768f",
            "amount": 5000.00
        },
        "customerDetails": {
            "id": "67bc002907fe47ec12",
            "accountId": "67a1541b87e86fcb70",
            "name": "John Smith"
        }
    }
}
```

#### Card Payment Request
```json
{
    "flowTemplateCode": "CREATE.IHC.REPAYMENT",
    "blueTapeCorrelationId": "1cd69455-7d7c-4292-a603-eb0ad921b16d",
    "createdBy": "BlueTape.LMS.ProcessDue",
    "details": {
        "date": "2025-03-21",
        "currency": "USD",
        "requestedAmount": 5000.00,
        "paymentMethod": "card",
        "drawDetails": {
            "id": "b62f3d66-21a5-4625-bba6-f8cec368768f",
            "amount": 5000.00
        },
        "customerDetails": {
            "id": "67bc002907fe47ec12",
            "accountId": "67a1541b87e86fcb70",
            "name": "John Smith"
        }
    }
}
```

## Files Created/Modified

### New Message Models
- `IhcRepaymentRequestMessage.cs` - Main message class
- `IhcRepaymentRequestDetails.cs` - Payment details
- `IhcRepaymentDrawDetails.cs` - Draw information
- `IhcRepaymentCustomerDetails.cs` - Customer information

### Strategy Implementation
- `IhcRepaymentStrategy.cs` - Strategy for handling IHC repayment requests

### Updated Files
- `DomainConstants.cs` - Added IhcRepayment constant
- `PaymentRequestType.cs` - Added IhcRepayment enum value
- `PaymentRequestConsumer.cs` - Added IHC repayment message handling
- `MessageProfile.cs` - Added AutoMapper configuration
- `DependencyRegistrar.cs` - Registered IhcRepaymentStrategy

## Strategy Implementation

### IhcRepaymentStrategy
```csharp
public class IhcRepaymentStrategy : IPaymentCreationStrategy
{
    public bool CanHandle(string flowTemplateCode)
    {
        return flowTemplateCode == DomainConstants.IhcRepayment;
    }

    public async Task ValidatePaymentRequest(...)
    {
        // Uses same validation as Draw Repayment
        await _paymentRequestValidator.ValidateDrawRepaymentPaymentRequest(createPaymentRequest, loan, ct);
    }

    public async Task SendNotification(...)
    {
        // No notifications sent (LMS handles its own notifications)
        await Task.CompletedTask;
    }

    public async Task ProcessAdditionalLogic(...)
    {
        // No additional processing needed
        await Task.CompletedTask;
    }
}
```

## Message Processing Flow

1. **LMS Sends Message**: LMS sends IHC repayment request to Payment Domain queue
2. **Consumer Receives**: PaymentRequestConsumer receives and routes to HandleIhcRepaymentMessage
3. **Message Parsing**: Message is parsed as IhcRepaymentRequestMessage
4. **AutoMapper**: Message is mapped to CreatePaymentRequestModel
5. **Unified Service**: UnifiedPaymentCreationService processes the request
6. **Strategy Selection**: IhcRepaymentStrategy is selected based on flow template code
7. **Validation**: Draw repayment validation is applied
8. **Payment Creation**: Payment request is created and persisted
9. **Result**: Payment Domain executes transaction and writes results back to LMS

## Key Differences from ManualPaymentPull

### Similarities
- Both handle pull payments from customers
- Both support ACH and Card payment methods
- Both are initiated by external systems

### Differences
- **Template Name**: `CREATE.IHC.REPAYMENT` vs `CREATE.MANUAL.PAYMENT.PULL`
- **Initiator**: LMS vs Manual processes
- **Message Structure**: Simplified structure focused on draw repayment
- **Validation**: Uses draw repayment validation logic
- **Notifications**: No notifications (LMS handles its own)

## Testing

### Integration Tests
- `IhcRepaymentIntegrationTests.cs` - Comprehensive test suite
- Tests both ACH and Card payment scenarios
- Validates message serialization/deserialization
- Confirms payment request creation

### Test Scenarios
1. **ACH Payment Creation** - Validates ACH payment request creation
2. **Card Payment Creation** - Validates Card payment request creation
3. **Message Serialization** - Tests JSON serialization/deserialization
4. **Strategy Selection** - Confirms correct strategy is used

## Usage Example

### From LMS (Node.js equivalent)
```javascript
// This is the .NET implementation equivalent of NodeJs achPayment.Service.ts drawRepayment()
// but for IHC payment as well

const ihcRepaymentRequest = {
    flowTemplateCode: "CREATE.IHC.REPAYMENT",
    blueTapeCorrelationId: generateCorrelationId(),
    createdBy: "BlueTape.LMS.ProcessDue",
    details: {
        date: "2025-03-21",
        currency: "USD",
        requestedAmount: 5000.00,
        paymentMethod: "ach", // or "card"
        drawDetails: {
            id: drawId,
            amount: 5000.00
        },
        customerDetails: {
            id: customerId,
            accountId: selectedBankAccountOrCardId,
            name: customerName
        }
    }
};

// Send to Payment Domain queue
await sendToPaymentQueue(ihcRepaymentRequest);
```

## Benefits

### 1. LMS Integration
- First time LMS initiates payment requests to Payment Domain
- Seamless integration without direct API calls
- Asynchronous processing with result callbacks

### 2. Unified Architecture
- Leverages new Strategy Pattern implementation
- Consistent with other payment types
- Easy to maintain and extend

### 3. Dual Payment Method Support
- Single template handles both ACH and Card
- Payment method determined by message content
- Flexible for different customer preferences

### 4. Simplified Processing
- Reuses existing draw repayment validation
- No complex additional logic required
- Minimal notification overhead

## Migration Path

### Phase 1 ✅ Complete
- [x] Implemented IHC Repayment strategy
- [x] Created message models
- [x] Added consumer handling
- [x] Integrated with unified architecture

### Phase 2 🔄 Next Steps
- [ ] Deploy to staging environment
- [ ] Test with LMS integration
- [ ] Monitor payment processing
- [ ] Validate result callbacks to LMS

### Phase 3 🔄 Future
- [ ] Consider deprecating ManualPaymentPull if appropriate
- [ ] Optimize performance based on usage patterns
- [ ] Add additional validation if needed

## Conclusion

The IHC Repayment implementation successfully:
- ✅ **Replaces ManualPaymentPull** with a more specific template
- ✅ **Enables LMS Integration** for payment initiation
- ✅ **Supports Dual Payment Methods** (ACH and Card)
- ✅ **Leverages Unified Architecture** for consistency
- ✅ **Provides Comprehensive Testing** for validation

This implementation provides a solid foundation for LMS-initiated payment requests while maintaining the flexibility and consistency of the unified payment architecture.
