﻿using Amazon.SimpleNotificationService;
using BlueTape.AionServiceClient.DI;
using BlueTape.AzureKeyVault.Abstractions;
using BlueTape.AzureKeyVault.Services;
using BlueTape.CompanyClient.DI;
using BlueTape.EmailSender.Extensions;
using BlueTape.Integrations.Aion.AzureTableStorage.DI;
using BlueTape.InvoiceClient.DI;
using BlueTape.LinqpalClient.DI;
using BlueTape.LoanServiceClient.Extensions;
using BlueTape.PaymentService.Application.Abstractions.Mappers;
using BlueTape.PaymentService.Application.Abstractions.Processors;
using BlueTape.PaymentService.Application.Abstractions.Senders;
using BlueTape.PaymentService.Application.Abstractions.Senders.Payments;
using BlueTape.PaymentService.Application.Abstractions.Services;
using BlueTape.PaymentService.Application.Abstractions.Services.External;
using BlueTape.PaymentService.Application.Abstractions.Services.External.Ledger;
using BlueTape.PaymentService.Application.Abstractions.Services.Notification;
using BlueTape.PaymentService.Application.Abstractions.Services.PaymentRequestCreation;
using BlueTape.PaymentService.Application.Abstractions.Validators;
using BlueTape.PaymentService.Application.Behaviors;
using BlueTape.PaymentService.Application.Handlers.Commands.PaymentCommands;
using BlueTape.PaymentService.Application.Mappers;
using BlueTape.PaymentService.Application.Options;
using BlueTape.PaymentService.Application.Processors;
using BlueTape.PaymentService.Application.Senders;
using BlueTape.PaymentService.Application.Senders.Payments;
using BlueTape.PaymentService.Application.Services;
using BlueTape.PaymentService.Application.Services.External;
using BlueTape.PaymentService.Application.Services.External.Ledger;
using BlueTape.PaymentService.Application.Services.Notification;
using BlueTape.PaymentService.Application.Services.PaymentRequestCreation;
using BlueTape.PaymentService.Application.Services.PaymentRequestCreation.Decorators;
using BlueTape.PaymentService.Application.Services.PaymentRequestCreation.Strategies;
using BlueTape.PaymentService.Application.Validators;
using BlueTape.PaymentService.CompatibilityService.Abstractions.Senders;
using BlueTape.PaymentService.CompatibilityService.DI;
using BlueTape.PaymentService.CompatibilityService.Senders;
using BlueTape.PaymentService.DataAccess.DI;
using BlueTape.PaymentService.DataAccess.External.DI;
using BlueTape.PaymentService.DataAccess.Mongo.DI;
using BlueTape.PaymentService.Domain.Messages;
using BlueTape.PaymentService.PaymentFlowTemplatesEngine.DI;
using BlueTape.PaymentService.UnitOfWork.DI;
using BlueTape.Reporting.Application.DI;
using BlueTape.Services.AWSMessaging.Extensions;
using BlueTape.SNS.SlackNotification.Extensions;
using BlueTape.Utilities.Providers;
using MediatR;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using SendGrid;
using System.Reflection;

namespace BlueTape.PaymentService.Application.DI
{
    public static class DependencyRegistrar
    {
        public static void AddApplicationDependencies(this IServiceCollection services, IConfiguration config)
        {
            services.AddTransient<IOperationsService, OperationsService>();
            //services.AddTransient<IAionService, AionService>();
            services.AddTransient<IAionServiceV2, AionServiceV2>();
            services.AddTransient<ILedgerService, LedgerService>();
            services.AddSingleton<IKeyVaultService, KeyVaultService>();
            services.AddSingleton<IMemoryCache, MemoryCache>();
            services.AddScoped<IStatusService, StatusService>();
            services.UsePaymentFlowTemplatesEngine(config);

            services.AddScoped<ILedgerOperationBuilder, LedgerOperationBuilder>();
            services.AddTransient<IPaymentTransactionService, PaymentTransactionService>();
            services.AddTransient<IPaymentRequestCommandService, PaymentRequestCommandService>();
            services.AddTransient<IEventLogService, EventLogService>();
            services.AddTransient<IPaymentRequestService, PaymentRequestService>();
            // Register the new unified payment creation service
            services.AddTransient<IUnifiedPaymentCreationService, UnifiedPaymentCreationService>();

            // Register payment creation strategies
            services.AddTransient<IPaymentCreationStrategy, InvoicePaymentStrategy>();
            services.AddTransient<IPaymentCreationStrategy, LineOfCreditRepaymentStrategy>();
            services.AddTransient<IPaymentCreationStrategy, FactoringDisbursementStrategy>();
            services.AddTransient<IPaymentCreationStrategy, LineOfCreditDisbursementStrategy>();
            services.AddTransient<IPaymentCreationStrategy, InvoiceDisbursementV2Strategy>();
            services.AddTransient<IPaymentCreationStrategy, SubscriptionFeeStrategy>();
            services.AddTransient<IPaymentCreationStrategy, IhcRepaymentStrategy>();

            // Keep existing services for backward compatibility during transition
            services.AddTransient<IInvoicePaymentRequestCreationService, InvoicePaymentRequestCreationService>();
            services.AddTransient<IInvoicePaymentV2RequestCreationService, InvoicePaymentV2RequestCreationService>();
            services.AddTransient<IDrawRepaymentRequestCreationService, DrawRepaymentRequestCreationService>();
            services.AddTransient<IDrawRepaymentCardRequestCreationService, DrawRepaymentCardRequestCreationService>();
            services.AddTransient<IDrawRepaymentManualRequestCreationService, DrawRepaymentManualRequestCreationService>();
            services.AddTransient<IFinalPaymentRequestCreationService, FinalPaymentRequestCreationService>();
            services.AddTransient<IInvoiceDisbursementV2RequestCreationService, InvoiceDisbursementV2RequestCreationService>();
            services.AddTransient<IFactoringFinalPaymentRequestCreationService, FactoringFinalPaymentRequestCreationService>();
            services.AddTransient<IFactoringDisbursementRequestCreationService, FactoringDisbursementCreationService>();
            services.AddTransient<IDrawDisbursementRequestCreatorService, DrawDisbursementRequestCreatorService>();
            services.AddTransient<IManualPaymentPullCreationService, ManualPaymentPullCreationService>();
            services.AddTransient<ISubscriptionFeePaymentRequestCreationService, SubscriptionFeePaymentRequestCreationService>();
            services.Decorate<IDrawRepaymentRequestCreationService, DrawRepaymentRequestCreationDecorator>();

            services.AddTransient<IPaymentRequestPayableService, PaymentRequestPayableService>();
            services.AddTransient<IPaymentTransactionHistoryService, PaymentTransactionHistoryService>();
            services.AddTransient<ITransactionNumberService, TransactionNumberService>();
            services.AddTransient<ILedgerService, LedgerService>();
            services.AddTransient<IHelperService, HelperService>();
            services.AddTransient<IDateProvider, DateProvider>();
            services.AddTransient<IPaymentConfigService, PaymentConfigService>();

            services.AddSingleton<ISendGridClient, SendGridClient>(_ => new SendGridClient(config.GetSection("LP-SENDGRID-API-KEY").Value!));

            services.AddSnsSlackNotifications();
            services.AddTransient<ISlackNotificationService, SlackNotificationService>();
            services.AddTransient<IDatabaseNotificationService, DatabaseNotificationService>();
            services.AddTransient<INotificationReceiversService, NotificationReceiversService>();
            services.AddAWSService<IAmazonSimpleNotificationService>();
            services.AddEmailService();

            services.Configure<SlackNotificationOptions>(config.GetSection(SlackNotificationOptions.SectionName));
            services.Configure<TwilioTemplateOptions>(config.GetSection(TwilioTemplateOptions.TwilioTemplates));

            services.AddTransient<ICommandManagementService, CommandManagementService>();

            services.AddAutoMapper(typeof(ModelsProfile).GetTypeInfo().Assembly);

            services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(typeof(PullFromCustomerTransactionHandler).Assembly));
            services.AddSingleton(typeof(IPipelineBehavior<,>), typeof(LoggingBehavior<,>));

            services.AddMongoDataAccessDependencies();
            services.AddDataAccessDependencies(config);
            services.AddDataAccessExternalDependencies();
            services.AddUnitOfWork();

            services.AddTransient<IPaymentJobProcessor, PaymentJobProcessor>();
            services.AddScoped<IPaymentFlowServiceMessageSender, PaymentFlowServiceMessageSender>();
            services.AddScoped<IInvoiceSyncMessageSender, InvoiceSyncMessageSender>();
            services.AddScoped<IDrawRepaymentManualMessageSender, DrawRepaymentManualMessageSender>();
            services.AddScoped<IManualPaymentPullMessageSender, ManualPaymentPullMessageSender>();

            services.AddTransient<IPaymentRequestValidator, PaymentRequestValidator>();

            services.AddTransient<ITransactionStatusUpdateProcessor, TransactionStatusUpdateProcessor>();
            services.AddTransient<IFailedCommandsProcessor, FailedCommandsProcessor>();
            services.AddTransient<IRollbackCommandsProcessor, RollbackCommandsProcessor>();
            services.AddScoped<INotificationProcessor, NotificationProcessor>();
            services.AddScoped<ITransactionStatusMessageSender, TransactionStatusMessageSender>();
            services.AddScoped<IOperationSyncMessageSender, OperationSyncMessageSender>();
            services.AddScoped<ITransactionStatusUpdateService, TransactionStatusUpdateService>();
            services.AddScoped<ICompanyService, Services.CompanyService>();
            services.AddScoped<ILedgerPaymentMessageSender, LedgerPaymentMessageSender>();
            services.AddScoped<INotificationMessageSender, NotificationMessageSender>();
            services.AddScoped<IPublicTransactionNumberService, PublicTransactionNumberService>();
            services.AddAzureDataTableDependencies(config);
            services.UseCompatibilityService();

            services.AddCompanyServiceClient(config);
            services.AddAionServiceClient(config);
            services.AddInvoiceServiceClient(config);
            services.AddLinqpalServiceClient(config);
            services.AddLoanManagementServiceHttpClient(config);

            services.AddSqs(config);
            services.AddTransient<IConnectorMessageSender, ConnectorMessageSender>();

            services.AddTransient<ILoanManagementService, LoanManagementService>();
            services.AddTransient<IEmailNotificationService, EmailNotificationService>();
            services.AddTransient<ITransactionNotificationService, TransactionNotificationService>();

            services.AddReportApplicationDependencies(config);
        }

        private static void AddSqs(this IServiceCollection services, IConfiguration configuration)
        {
            services.Configure<ConnectorQueueOptions>(configuration.GetSection(nameof(ConnectorQueueOptions)));
            services.AddSqsEndpoint<ConnectorMessagePayload, ConnectorQueueOptions>(options => options.TopicName);
        }
    }
}
