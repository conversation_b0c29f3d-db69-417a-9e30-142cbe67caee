using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BlueTape.PaymentService.Application.Abstractions.Services;
using BlueTape.PaymentService.DataAccess.Abstractions.Repositories;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Models;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace BlueTape.PaymentService.Application.Services;

public class PaymentConfigService(
    IPaymentConfigRepository paymentConfigRepository,
    IDateProvider dateProvider,
    ILogger<PaymentConfigService> logger) : IPaymentConfigService
{
    private const string AionDailyLimitConfigKeyPrefix = "AION_DAILY_LIMIT_CONFIG";

    public async Task<AionDailyLimitConfig> GetAionDailyLimitConfig(PaymentSubscriptionType subscriptionType, CancellationToken ct)
    {
        var configKey = GetSubscriptionConfigKey(subscriptionType);
        var configEntity = await paymentConfigRepository.GetByConfigKey(configKey, ct);

        if (configEntity == null)
        {
            return new AionDailyLimitConfig();
        }

        try
        {
            return JsonSerializer.Deserialize<AionDailyLimitConfig>(configEntity.ConfigValue)
                   ?? new AionDailyLimitConfig();
        }
        catch (JsonException ex)
        {
            logger.LogError(ex, "Failed to deserialize Aion daily limit config for subscription: {SubscriptionType}", subscriptionType);
            return new AionDailyLimitConfig();
        }
    }

    public async Task SetAionDailyLimitExceeded(PaymentTransactionType transactionType, PaymentSubscriptionType subscriptionType, CancellationToken ct)
    {
        var config = await GetAionDailyLimitConfig(subscriptionType, ct);

        switch (transactionType)
        {
            case PaymentTransactionType.WirePush:
                config.WireLimitExceeded = true;
                break;
            case PaymentTransactionType.InstantPush:
                config.InstantLimitExceeded = true;
                break;
            case PaymentTransactionType.AchPull:
                config.AchPullLimitExceeded = true;
                break;
            case PaymentTransactionType.AchPush:
                config.AchPushLimitExceeded = true;
                break;
            default:
                logger.LogWarning("Unsupported transaction type for daily limit: {TransactionType}", transactionType);
                return;
        }

        config.LastUpdated = dateProvider.CurrentDateTime;
        await SaveAionDailyLimitConfig(config, subscriptionType, ct);

        logger.LogWarning("Aion daily limit exceeded for transaction type: {TransactionType}, subscription: {SubscriptionType}",
            transactionType, subscriptionType);
    }

    public async Task ResetAionDailyLimits(PaymentSubscriptionType subscriptionType, CancellationToken ct)
    {
        var config = new AionDailyLimitConfig
        {
            LastUpdated = dateProvider.CurrentDateTime
        };

        await SaveAionDailyLimitConfig(config, subscriptionType, ct);
        logger.LogInformation("Aion daily limits reset for subscription: {SubscriptionType} at {ResetTime}",
            subscriptionType, dateProvider.CurrentDateTime);
    }

    public async Task<bool> IsAionDailyLimitExceeded(PaymentTransactionType transactionType, PaymentSubscriptionType subscriptionType, CancellationToken ct)
    {
        var config = await GetAionDailyLimitConfig(subscriptionType, ct);

        return transactionType switch
        {
            PaymentTransactionType.WirePush => config.WireLimitExceeded,
            PaymentTransactionType.InstantPush => config.InstantLimitExceeded,
            PaymentTransactionType.AchPull => config.AchPullLimitExceeded,
            PaymentTransactionType.AchPush => config.AchPushLimitExceeded,
            _ => false
        };
    }

    public async Task ResetAllAionDailyLimits(CancellationToken ct)
    {
        // Reset all subscription limits
        var subscriptionTypes = new[] { PaymentSubscriptionType.SUBSCRIPTION1, PaymentSubscriptionType.SUBSCRIPTION2, PaymentSubscriptionType.SUBSCRIPTION3 };

        foreach (var subscriptionType in subscriptionTypes)
        {
            await ResetAionDailyLimits(subscriptionType, ct);
        }

        logger.LogInformation("All Aion daily limits for all subscriptions reset at {ResetTime}", dateProvider.CurrentDateTime);
    }

    public async Task<Dictionary<PaymentSubscriptionType, AionDailyLimitConfig>> GetAllAionDailyLimitConfigs(CancellationToken ct)
    {
        var subscriptionTypes = new[] { PaymentSubscriptionType.SUBSCRIPTION1, PaymentSubscriptionType.SUBSCRIPTION2, PaymentSubscriptionType.SUBSCRIPTION3 };
        var configKeys = subscriptionTypes.Select(GetSubscriptionConfigKey).ToList();
        
        var configEntities = await paymentConfigRepository.GetByConfigKeys(configKeys, ct);
        var configDict = new Dictionary<PaymentSubscriptionType, AionDailyLimitConfig>();

        foreach (var subscriptionType in subscriptionTypes)
        {
            var configKey = GetSubscriptionConfigKey(subscriptionType);
            var configEntity = configEntities.FirstOrDefault(x => x.ConfigKey == configKey);

            if (configEntity == null)
            {
                configDict[subscriptionType] = new AionDailyLimitConfig();
            }
            else
            {
                try
                {
                    var config = JsonSerializer.Deserialize<AionDailyLimitConfig>(configEntity.ConfigValue)
                                  ?? new AionDailyLimitConfig();
                    configDict[subscriptionType] = config;
                }
                catch (JsonException ex)
                {
                    logger.LogError(ex, "Failed to deserialize Aion daily limit config for subscription: {SubscriptionType}", subscriptionType);
                    configDict[subscriptionType] = new AionDailyLimitConfig();
                }
            }
        }

        return configDict;
    }

    private async Task SaveAionDailyLimitConfig(AionDailyLimitConfig config, PaymentSubscriptionType subscriptionType, CancellationToken ct)
    {
        var configKey = GetSubscriptionConfigKey(subscriptionType);
        var configEntity = await paymentConfigRepository.GetByConfigKey(configKey, ct);
        var configJson = JsonSerializer.Serialize(config);

        if (configEntity == null)
        {
            configEntity = new PaymentConfigEntity
            {
                ConfigKey = configKey,
                ConfigValue = configJson,
                Description = $"Aion daily limit configuration for subscription {subscriptionType}",
                CreatedAt = dateProvider.CurrentDateTime,
                CreatedBy = nameof(PaymentConfigService)
            };
            await paymentConfigRepository.Add(configEntity, ct);
        }
        else
        {
            configEntity.ConfigValue = configJson;
            configEntity.UpdatedAt = dateProvider.CurrentDateTime;
            configEntity.UpdatedBy = nameof(PaymentConfigService);
            await paymentConfigRepository.Update(configEntity, ct);
        }
    }

    private static string GetSubscriptionConfigKey(PaymentSubscriptionType subscriptionType)
    {
        return $"{AionDailyLimitConfigKeyPrefix}_{subscriptionType}";
    }
}
